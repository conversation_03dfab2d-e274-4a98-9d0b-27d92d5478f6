#!/usr/bin/env python3
"""
Test script to verify CSV functionality with sample data
"""

import pandas as pd
import json
import os

def save_to_csv(data_dict):
    """
    Save the dictionary data to two CSV files:
    1. keyword_analysis.csv - contains keyword and analysis attributes
    2. keyword_results.csv - contains keyword and top_5_results attributes
    """
    if not data_dict:
        print("No data to save")
        return
    
    keyword = data_dict.get('keyword', '')
    analysis = data_dict.get('analysis', {})
    top_5_results = data_dict.get('top_5_results', [])
    
    # Create analysis CSV
    analysis_data = {
        'keyword': [keyword],
        'user_search_intent': [analysis.get('user_search_intent', '')],
        'existing_content_formats': ['; '.join(analysis.get('existing_content_formats', []))],
        'content_gaps': ['; '.join(analysis.get('content_gaps', []))]
    }
    
    analysis_df = pd.DataFrame(analysis_data)
    analysis_df.to_csv('test_keyword_analysis.csv', index=False, encoding='utf-8')
    print("Analysis data saved to test_keyword_analysis.csv")
    
    # Create results CSV
    results_data = []
    for result in top_5_results:
        result_row = {
            'keyword': keyword,
            'rank': result.get('rank', ''),
            'title': result.get('title', ''),
            'url': result.get('url', ''),
            'summary': result.get('summary', '')
        }
        results_data.append(result_row)
    
    if results_data:
        results_df = pd.DataFrame(results_data)
        results_df.to_csv('test_keyword_results.csv', index=False, encoding='utf-8')
        print("Results data saved to test_keyword_results.csv")
    else:
        print("No results data to save")

def test_csv_functionality():
    """Test the CSV saving functionality with sample data"""
    
    # Sample data structure based on the expected JSON format
    sample_data = {
        "keyword": "Python Programming",
        "analysis": {
            "user_search_intent": "Users are looking for comprehensive learning resources, tutorials, and practical examples for Python programming.",
            "existing_content_formats": [
                "Tutorial websites",
                "Video courses",
                "Documentation pages",
                "Blog posts"
            ],
            "content_gaps": [
                "Advanced debugging techniques",
                "Real-world project examples",
                "Performance optimization guides"
            ]
        },
        "top_5_results": [
            {
                "rank": 1,
                "title": "Python.org - Official Python Website",
                "url": "https://www.python.org/",
                "summary": "Official Python website with documentation, downloads, and community resources."
            },
            {
                "rank": 2,
                "title": "Python Tutorial - W3Schools",
                "url": "https://www.w3schools.com/python/",
                "summary": "Comprehensive Python tutorial covering basics to advanced topics."
            },
            {
                "rank": 3,
                "title": "Learn Python - Codecademy",
                "url": "https://www.codecademy.com/learn/learn-python-3",
                "summary": "Interactive Python course with hands-on exercises."
            },
            {
                "rank": 4,
                "title": "Python Programming - GeeksforGeeks",
                "url": "https://www.geeksforgeeks.org/python-programming-language/",
                "summary": "Python programming tutorials and examples for all skill levels."
            },
            {
                "rank": 5,
                "title": "Real Python",
                "url": "https://realpython.com/",
                "summary": "In-depth Python tutorials and articles for practical learning."
            }
        ]
    }
    
    print("Testing CSV functionality with sample data...")
    print(f"Sample data: {json.dumps(sample_data, indent=2)}")
    print("\n" + "="*50)
    
    # Save to CSV
    save_to_csv(sample_data)
    
    print("\n" + "="*50)
    print("Testing completed!")
    
    # Verify files were created
    if os.path.exists('test_keyword_analysis.csv'):
        print("✓ test_keyword_analysis.csv created successfully")
        # Read and display first few rows
        df = pd.read_csv('test_keyword_analysis.csv')
        print("Analysis CSV content:")
        print(df.to_string(index=False))
    else:
        print("✗ test_keyword_analysis.csv not found")
    
    print("\n" + "-"*30)
    
    if os.path.exists('test_keyword_results.csv'):
        print("✓ test_keyword_results.csv created successfully")
        # Read and display first few rows
        df = pd.read_csv('test_keyword_results.csv')
        print("Results CSV content:")
        print(df.to_string(index=False))
    else:
        print("✗ test_keyword_results.csv not found")

if __name__ == "__main__":
    test_csv_functionality()
