

## 核心需求
1. 管理文件结构，除程序主入口外，主目录不要有太多文件
2. 无用的测试文件和被淘汰的文档文件需要被即时删除
3. 以下功能的实现方式为一个函数
4. 存储结果统一存放，易于寻找
5. 这个功能要求较为简单，需要简化系统实现
6. 生成的文档放置在docs目录下
7. 遵循 knowledge/coding_extra_rules.md 的编码要求


## 核心工作流程
我会提供一个关键词表， 他拥有google keyword planner 的所有属性，包括搜索量，页首出价等，调用google 2.5 flash 进行高优先级分析
SERP_Scout 为一个函数，对高优先级关键词，批量采集其Google Top 10-20的搜索结果（标题、描述、HTML内容）。
Strategic_Analyst对采集到的SERP数据进行批量化的LLM分析，为每个关键词生成一份**“上下文认知档案”**，包含：用户意图、内容格式、核心子主题、竞争强度。





## API配置
- **Google LLM API Key**: AIzaSyBWPiNDni24gEzdZUzNUhVxws6prDHB4Yo