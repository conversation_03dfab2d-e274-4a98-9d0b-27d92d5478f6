# Browser Content Analysis Tool

这个工具使用Google Gemini API来分析关键词的搜索结果，并将分析数据保存为CSV文件。

## 功能特性

- 🔍 分析关键词的前5个Google搜索结果
- 📊 识别用户搜索意图
- 📝 分析现有内容格式
- 🎯 发现内容空白点
- 💾 自动保存为CSV文件
- 🔄 支持批量处理多个关键词

## 文件说明

### 核心文件

1. **`get_browser_content_llm.py`** - 原始脚本，已更新API key和CSV保存功能
2. **`enhanced_browser_content_llm.py`** - 增强版本，支持批量处理和更好的错误处理
3. **`test_csv_functionality.py`** - 测试CSV功能的脚本
4. **`example_usage.py`** - 使用示例脚本

### 输出文件

- **`keyword_analysis.csv`** - 包含关键词和分析属性
- **`keyword_results.csv`** - 包含关键词和前5个搜索结果

## CSV文件结构

### keyword_analysis.csv
| 列名 | 描述 |
|------|------|
| keyword | 搜索关键词 |
| user_search_intent | 用户搜索意图分析 |
| existing_content_formats | 现有内容格式（用分号分隔） |
| content_gaps | 内容空白点（用分号分隔） |
| timestamp | 分析时间戳 |

### keyword_results.csv
| 列名 | 描述 |
|------|------|
| keyword | 搜索关键词 |
| rank | 搜索结果排名 (1-5) |
| title | 页面标题 |
| url | 页面URL |
| summary | 内容摘要 |
| timestamp | 分析时间戳 |

## 使用方法

### 1. 基本使用

```python
import asyncio
from enhanced_browser_content_llm import get_browser_content, save_to_csv

# 分析单个关键词
async def analyze_keyword():
    result = await get_browser_content("你的关键词")
    if result:
        save_to_csv(result)

asyncio.run(analyze_keyword())
```

### 2. 批量处理

```python
import asyncio
from enhanced_browser_content_llm import process_multiple_keywords

# 分析多个关键词
keywords = [
    "人工智能",
    "机器学习",
    "深度学习"
]

asyncio.run(process_multiple_keywords(keywords))
```

### 3. 运行示例

```bash
# 运行原始脚本
python get_browser_content_llm.py

# 运行增强版本
python enhanced_browser_content_llm.py

# 运行交互式示例
python example_usage.py

# 测试CSV功能
python test_csv_functionality.py
```

## API密钥配置

脚本中已配置了两个API密钥：
- `AIzaSyBWPiNDni24gEzdZUzNUhVxws6prDHB4Yo`
- `AIzaSyBxVGph11TTLKKfGBXH3GVPIXriYaroV9E`

如果需要使用自定义API密钥：

```python
result = await get_browser_content("关键词", api_key="你的API密钥")
```

## 依赖包

确保安装以下Python包：

```bash
pip install google-genai pandas
```

## 注意事项

1. **API调用时间**: 每次API调用大约需要1-2分钟
2. **错误处理**: 脚本包含完整的错误处理和重试机制
3. **文件编码**: CSV文件使用UTF-8编码保存
4. **追加模式**: 批量处理时，数据会追加到现有CSV文件中

## 示例输出

### 分析结果示例
```
关键词: COVID-19 Antigen Rapid Test
用户搜索意图: 用户主要寻求如何执行和解释COVID-19抗原快速检测的信息...
内容格式: 产品页面; 信息博客文章; 官方健康指南
内容空白: 特定人群的详细指导; 地区特定的处理说明...
```

### CSV文件示例
分析文件会包含关键词的完整分析，结果文件会包含每个搜索结果的详细信息。

## 故障排除

1. **API错误**: 如果遇到500错误，尝试使用另一个API密钥
2. **JSON解析错误**: 检查API响应格式，可能需要调整解析逻辑
3. **文件权限**: 确保脚本有写入当前目录的权限

## 更新日志

- ✅ 添加了CSV保存功能
- ✅ 支持批量处理
- ✅ 改进了错误处理
- ✅ 添加了时间戳
- ✅ 创建了测试和示例脚本
