#!/usr/bin/env python3
"""
Example usage of the enhanced browser content analysis tool
"""

import asyncio
from enhanced_browser_content_llm import get_browser_content, process_multiple_keywords, save_to_csv

async def example_single_keyword():
    """Example of processing a single keyword"""
    print("=== Single Keyword Example ===")
    
    keyword = "Machine Learning Algorithms"
    print(f"Processing keyword: {keyword}")
    
    # Get the analysis
    result = await get_browser_content(keyword)
    
    if result:
        print("\n✓ Successfully retrieved data!")
        print(f"Keyword: {result['keyword']}")
        print(f"User Intent: {result['analysis']['user_search_intent'][:100]}...")
        print(f"Number of results: {len(result['top_5_results'])}")
        
        # Save to CSV
        save_to_csv(result)
        print("✓ Data saved to CSV files")
    else:
        print("✗ Failed to retrieve data")

async def example_multiple_keywords():
    """Example of processing multiple keywords"""
    print("\n=== Multiple Keywords Example ===")
    
    keywords = [
        "Artificial Intelligence",
        "Deep Learning",
        "Natural Language Processing"
    ]
    
    print(f"Processing {len(keywords)} keywords...")
    await process_multiple_keywords(keywords)

async def example_with_custom_api_key():
    """Example using a custom API key"""
    print("\n=== Custom API Key Example ===")
    
    # You can use either of the provided API keys
    api_key_1 = 'AIzaSyBWPiNDni24gEzdZUzNUhVxws6prDHB4Yo'
    api_key_2 = 'AIzaSyBxVGph11TTLKKfGBXH3GVPIXriYaroV9E'
    
    keyword = "Cloud Computing"
    
    # Try with first API key
    try:
        result = await get_browser_content(keyword, api_key_1)
        if result:
            save_to_csv(result)
            print(f"✓ Successfully processed '{keyword}' with API key 1")
        else:
            print(f"✗ Failed with API key 1, trying API key 2...")
            result = await get_browser_content(keyword, api_key_2)
            if result:
                save_to_csv(result)
                print(f"✓ Successfully processed '{keyword}' with API key 2")
    except Exception as e:
        print(f"✗ Error: {e}")

def view_csv_results():
    """View the results from CSV files"""
    print("\n=== Viewing CSV Results ===")
    
    try:
        import pandas as pd
        
        # Read analysis results
        if os.path.exists('keyword_analysis.csv'):
            print("\n📊 Analysis Results:")
            df_analysis = pd.read_csv('keyword_analysis.csv')
            print(f"Total keywords analyzed: {len(df_analysis)}")
            for _, row in df_analysis.iterrows():
                print(f"- {row['keyword']}")
        
        # Read search results
        if os.path.exists('keyword_results.csv'):
            print("\n🔍 Search Results:")
            df_results = pd.read_csv('keyword_results.csv')
            print(f"Total search results: {len(df_results)}")
            
            # Group by keyword
            for keyword in df_results['keyword'].unique():
                keyword_results = df_results[df_results['keyword'] == keyword]
                print(f"\n{keyword} ({len(keyword_results)} results):")
                for _, result in keyword_results.iterrows():
                    print(f"  {result['rank']}. {result['title']}")
    
    except ImportError:
        print("pandas not available for viewing results")
    except Exception as e:
        print(f"Error viewing results: {e}")

async def main():
    """Main function to run examples"""
    print("🚀 Browser Content Analysis Tool Examples")
    print("=" * 50)
    
    # Choose which example to run
    print("\nChoose an example to run:")
    print("1. Single keyword analysis")
    print("2. Multiple keywords analysis")
    print("3. Custom API key example")
    print("4. View existing CSV results")
    
    choice = input("\nEnter your choice (1-4): ").strip()
    
    if choice == "1":
        await example_single_keyword()
    elif choice == "2":
        await example_multiple_keywords()
    elif choice == "3":
        await example_with_custom_api_key()
    elif choice == "4":
        view_csv_results()
    else:
        print("Invalid choice. Running single keyword example...")
        await example_single_keyword()

if __name__ == "__main__":
    import os
    asyncio.run(main())
