from google import genai
from google.genai import types
import asyncio
import json
import pandas as pd
import os
from datetime import datetime
import time

def save_to_csv(data_dict, append_mode=False, file_prefix="batch"):
    """
    Save the dictionary data to two CSV files with batch prefix
    """
    if not data_dict:
        print("No data to save")
        return
    
    keyword = data_dict.get('keyword', '')
    analysis = data_dict.get('analysis', {})
    top_5_results = data_dict.get('top_5_results', [])
    
    # Add timestamp for tracking
    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    
    # Create analysis CSV
    analysis_data = {
        'keyword': [keyword],
        'user_search_intent': [analysis.get('user_search_intent', '')],
        'existing_content_formats': ['; '.join(analysis.get('existing_content_formats', []))],
        'content_gaps': ['; '.join(analysis.get('content_gaps', []))],
        'timestamp': [timestamp]
    }
    
    analysis_df = pd.DataFrame(analysis_data)
    analysis_file = f'{file_prefix}_keyword_analysis.csv'
    
    # Save analysis data
    if append_mode and os.path.exists(analysis_file):
        analysis_df.to_csv(analysis_file, mode='a', header=False, index=False, encoding='utf-8')
        print(f"✓ Analysis data appended to {analysis_file}")
    else:
        analysis_df.to_csv(analysis_file, index=False, encoding='utf-8')
        print(f"✓ Analysis data saved to {analysis_file}")
    
    # Create results CSV
    results_data = []
    for result in top_5_results:
        result_row = {
            'keyword': keyword,
            'rank': result.get('rank', ''),
            'title': result.get('title', ''),
            'url': result.get('url', ''),
            'summary': result.get('summary', ''),
            'timestamp': timestamp
        }
        results_data.append(result_row)
    
    if results_data:
        results_df = pd.DataFrame(results_data)
        results_file = f'{file_prefix}_keyword_results.csv'
        
        if append_mode and os.path.exists(results_file):
            results_df.to_csv(results_file, mode='a', header=False, index=False, encoding='utf-8')
            print(f"✓ Results data appended to {results_file}")
        else:
            results_df.to_csv(results_file, index=False, encoding='utf-8')
            print(f"✓ Results data saved to {results_file}")
    else:
        print("No results data to save")

async def get_browser_content(search_content: str, api_key: str = 'AIzaSyBxVGph11TTLKKfGBXH3GVPIXriYaroV9E') -> dict:
    """
    Get browser content analysis for a given search term
    """
    try:
        # Configure the client
        client = genai.Client(api_key=api_key)

        # Define the grounding tool
        grounding_tool = types.Tool(
            google_search=types.GoogleSearch()
        )

        url_tool = types.Tool(url_context=types.UrlContext())

        # Configure generation settings
        config = types.GenerateContentConfig(
            tools=[grounding_tool, url_tool]
        )

        print(f"🔍 Starting analysis for: {search_content}")

        # Make the request
        response = client.models.generate_content(
            model="gemini-2.5-flash",
            contents=f'''
            Analyze the top 5 Google search results for the keyword `{search_content}`.

    **Objective:**
    Identify the user search intent, content gaps, and existing content formats for the given keyword.

    **Process:**
    1.  Perform a Google search for `{search_content}` to retrieve the top 5 search results.
    2.  For each of the top 5 URLs, analyze the content.
    3.  Based on the analysis of all results, provide a summary that includes:
        *   **User Search Intent:** Determine the primary reason a user is searching for this keyword (e.g., informational, navigational, transactional, commercial investigation).
        *   **Existing Content Formats:** Identify the common formats of the top-ranking content (e.g., blog posts, videos, product pages, reviews, guides, news articles).
        *   **Content Gaps:** Pinpoint relevant subtopics, user questions, or angles that are not well-covered by the existing top results.

    **Output:**
    Return the findings in a structured JSON format as follows:

    {{
      "keyword": "{search_content}",
      "analysis": {{
        "user_search_intent": "...",
        "existing_content_formats": [
          "...",
          "..."
        ],
        "content_gaps": [
          "...",
          "..."
        ]
      }},
      "top_5_results": [
        {{
          "rank": 1,
          "title": "...",
          "url": "...",
          "summary": "..."
        }},
        {{
          "rank": 2,
          "title": "...",
          "url": "...",
          "summary": "..."
        }},
        {{
          "rank": 3,
          "title": "...",
          "url": "...",
          "summary": "..."
        }},
        {{
          "rank": 4,
          "title": "...",
          "url": "...",
          "summary": "..."
        }},
        {{
          "rank": 5,
          "title": "...",
          "url": "...",
          "summary": "..."
        }}
      ]
    }}

            ''',
            config=config,
        )

        # Parse the response
        cleared_text = response.text.strip('\n').strip('```').split('json')[1]
        dic = json.loads(cleared_text)
        
        print(f"✅ Successfully processed: {dic.get('keyword', 'Unknown')}")
        return dic
        
    except json.JSONDecodeError as e:
        print(f"❌ Error parsing JSON response for {search_content}: {e}")
        return None
    except Exception as e:
        print(f"❌ Error during API request for {search_content}: {e}")
        return None

async def process_batch(keywords_batch, batch_name, api_keys):
    """
    Process a batch of keywords concurrently
    """
    print(f"\n🚀 Starting {batch_name} processing...")
    print(f"Keywords: {', '.join(keywords_batch)}")
    print("-" * 60)
    
    start_time = time.time()
    
    # Create tasks for concurrent processing
    tasks = []
    for i, keyword in enumerate(keywords_batch):
        # Use different API keys to avoid rate limiting
        api_key = api_keys[i % len(api_keys)]
        task = get_browser_content(keyword, api_key)
        tasks.append(task)
    
    # Execute all tasks concurrently
    results = await asyncio.gather(*tasks, return_exceptions=True)
    
    end_time = time.time()
    processing_time = end_time - start_time
    
    # Process results and save to CSV
    successful_results = []
    failed_keywords = []
    
    for i, result in enumerate(results):
        keyword = keywords_batch[i]
        
        if isinstance(result, Exception):
            print(f"❌ Failed to process {keyword}: {result}")
            failed_keywords.append(keyword)
        elif result is None:
            print(f"❌ No data returned for {keyword}")
            failed_keywords.append(keyword)
        else:
            # Save to CSV (append mode after first result)
            append_mode = len(successful_results) > 0
            save_to_csv(result, append_mode=append_mode, file_prefix=batch_name.lower().replace(" ", "_"))
            successful_results.append(result)
    
    print(f"\n📊 {batch_name} Results:")
    print(f"⏱️  Processing time: {processing_time:.2f} seconds")
    print(f"✅ Successful: {len(successful_results)}/{len(keywords_batch)} keywords")
    print(f"❌ Failed: {len(failed_keywords)} keywords")
    
    if failed_keywords:
        print(f"Failed keywords: {', '.join(failed_keywords)}")
    
    return successful_results, failed_keywords, processing_time

async def main():
    """
    Main function to test batch processing
    """
    print("🧪 BATCH PROCESSING TEST")
    print("=" * 60)
    
    # Define the 6 keywords split into 2 groups
    group1_keywords = [
        "Anaplasma Antibody",
        "Brucella", 
        "Campylobacter"
    ]
    
    group2_keywords = [
        "Clostridium",
        "Leptospira",
        "Paratyphoid"
    ]
    
    # API keys for load balancing
    api_keys = [
        'AIzaSyBWPiNDni24gEzdZUzNUhVxws6prDHB4Yo',
        'AIzaSyBxVGph11TTLKKfGBXH3GVPIXriYaroV9E'
    ]
    
    total_start_time = time.time()
    
    # Process Group 1
    group1_results, group1_failed, group1_time = await process_batch(
        group1_keywords, "Group 1", api_keys
    )
    
    print("\n" + "="*60)
    print("⏸️  Waiting 10 seconds between batches...")
    await asyncio.sleep(10)
    
    # Process Group 2
    group2_results, group2_failed, group2_time = await process_batch(
        group2_keywords, "Group 2", api_keys
    )
    
    total_end_time = time.time()
    total_time = total_end_time - total_start_time
    
    # Final summary
    print("\n" + "="*60)
    print("🏁 FINAL BATCH PROCESSING SUMMARY")
    print("="*60)
    
    total_successful = len(group1_results) + len(group2_results)
    total_failed = len(group1_failed) + len(group2_failed)
    total_keywords = len(group1_keywords) + len(group2_keywords)
    
    print(f"📈 Overall Statistics:")
    print(f"   Total keywords processed: {total_keywords}")
    print(f"   ✅ Successful: {total_successful}")
    print(f"   ❌ Failed: {total_failed}")
    print(f"   📊 Success rate: {(total_successful/total_keywords)*100:.1f}%")
    
    print(f"\n⏱️  Timing Analysis:")
    print(f"   Group 1 processing time: {group1_time:.2f} seconds")
    print(f"   Group 2 processing time: {group2_time:.2f} seconds")
    print(f"   Total elapsed time: {total_time:.2f} seconds")
    print(f"   Time saved vs sequential: {(group1_time + group2_time) - total_time:.2f} seconds")
    
    print(f"\n📁 Output Files:")
    print(f"   Group 1: group_1_keyword_analysis.csv, group_1_keyword_results.csv")
    print(f"   Group 2: group_2_keyword_analysis.csv, group_2_keyword_results.csv")
    
    # Verify files were created
    files_to_check = [
        "group_1_keyword_analysis.csv",
        "group_1_keyword_results.csv", 
        "group_2_keyword_analysis.csv",
        "group_2_keyword_results.csv"
    ]
    
    print(f"\n🔍 File Verification:")
    for file_name in files_to_check:
        if os.path.exists(file_name):
            size = os.path.getsize(file_name)
            print(f"   ✅ {file_name} ({size} bytes)")
        else:
            print(f"   ❌ {file_name} (missing)")

if __name__ == "__main__":
    asyncio.run(main())
