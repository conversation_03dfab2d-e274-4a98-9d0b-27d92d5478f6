# LeadAgent Operational Workflow (Version 3.1.0)
# Last Updated: 2025-06-30 15:00:00 (UTC)

This document outlines the standard lifecycle for all development tasks.

### Phase 0: Intent Clarification & Structuring
*   **Goal:** Convert a high-level human idea into a structured Intent file.
*   **Human Role:** Provide initial idea. Review and clarify details.
*   **LLM Role (Mode 1: Analysis):** Ask clarifying questions, identify ambiguities, and structure the conversation into a draft `[project_name].intent.yaml` file.
*   **Output:** A confirmed `[project_name].intent.yaml` file.

### Phase 0.5: Request Classification & Documentation Alignment (NEW v3.3.0)
*   **Goal:** Classify incoming requests and ensure proper documentation alignment before any code implementation.
*   **Trigger:** ANY request that involves code changes, new features, or system modifications.
*   **Human Role:** Confirm classification and approve documentation updates.
*   **LLM Role (Mode 1: Analysis):**
    *   **Mandatory Classification:** Categorize request as:
        - **Type A: New Feature** → Requires Phase 1-2 documentation before Phase 3 implementation
        - **Type B: Bug Fix** → Requires root cause analysis and impact assessment
        - **Type C: Documentation Update** → Direct documentation modification allowed
        - **Type D: Configuration Change** → Requires configuration impact analysis
    *   **Documentation Alignment Check:** Before ANY code implementation:
        1. Load and review existing documentation (specs, architecture, API docs)
        2. Identify gaps between current documentation and proposed changes
        3. Update documentation FIRST to reflect intended changes
        4. Cross-validate documentation consistency across all related files
*   **Mandatory Gate:** NO code implementation may proceed without completed documentation alignment.
*   **Output:** Updated documentation that accurately reflects the intended system state BEFORE implementation.

### Phase 1: Formal Specification & Architectural Design
*   **Goal:** Elaborate the intent into a detailed, verifiable specification and a high-level technical design.
*   **Human Role:** Make key architectural decisions (e.g., platforms, core technologies). Review the formal model.
*   **LLM Role (Mode 1: Analysis):**
    *   Propose architectural options based on constraints in `deployment.config.toml`.
    *   Translate user stories from `.intent.yaml` into a formal specification in `[project_name].spec.md` (using Gherkin, etc.).
    *   For critical logic, generate a draft formal model in `[project_name].tla`.
*   **Note on Brownfield/Refactoring Projects (重要提示: 关于重构类项目):**
    *   When a project involves refactoring or replacing an existing system, Phase 1 MUST include a **"Code Archaeology"** sub-process.
    *   **Goal:** To discover and preserve critical business logic from the legacy codebase that may not be present in formal documentation.
    *   **Process:**
        1.  **Draft New Specs:** First, create a clean, ideal specification based on the project's high-level intent.
        2.  **Systematic Code Review:** Conduct a systematic review of the legacy codebase to identify key functionalities, "unwritten rules", and edge cases.
            *   **For Simple Systems (<50 functions):** Focus on core business logic, main error patterns, key data flows
            *   **For Complex Systems (≥50 functions):** Conduct deep analysis including:
                *   **Error Pattern Analysis:** Categorize and document all error handling strategies
                *   **State Management Analysis:** Map all state machines, transitions, and validation rules
                *   **Concurrency Pattern Analysis:** Document locking, transactions, resource management
                *   **Data Validation Analysis:** Catalog input validation, edge cases, constraint handling
                *   **Performance Pattern Analysis:** Identify bottlenecks, caching, optimization strategies
        3.  **Gap Analysis:** Compare the findings from the code review against the new draft specifications.
        4.  **Merge and Refine:** Update the new specification documents (`.spec.md`) to incorporate any discovered, valid, and desired logic from the legacy code.
        5.  **Implementation Pattern Documentation (Complex Systems Only):** Create detailed implementation pattern documents to preserve critical knowledge for Phase 2.
    *   This ensures the new design is both forward-looking and respects the validated logic of the past, preventing functional regressions.
*   **Output:** Confirmed `.spec.md`, `.tla` (if needed), and `deployment.config.toml` files, which are validated against both future intent and past implementation.

### Phase 1.75: Implementation Blueprinting (实现蓝图规划)
*   **Goal:** To create a complete, file-by-file, function-by-function blueprint of the target codebase before any implementation code is written. This minimizes the creative burden during the coding phase, reducing errors and ensuring consistency.
*   **Scope Distinction from Phase 1:**
    *   **Phase 1 Focus:** Understanding and documenting WHAT the legacy system does (business logic, error patterns, state management)
    *   **Phase 1.75 Focus:** Designing HOW the new system will be structured (file organization, function signatures, module dependencies)
*   **Human Role:** Review and approve the detailed code structure and function signatures.
*   **LLM Role (Mode 1: Analysis):**
    *   Synthesize information from all Phase 1 artifacts (`.spec.md`, `architecture.md`, `services_interfaces.md`, implementation pattern docs).
    *   **Apply legacy patterns to new structure:** Use Phase 1's implementation pattern analysis to inform new code organization.
    *   Generate a `code_structure.md` document that outlines every target file and provides Go function stubs (including function signatures and comments) for each.
    *   **For Complex Systems:** Reference Phase 1's detailed implementation pattern documents to ensure blueprint consistency.
*   **Output:** A confirmed `plans/implementation/code_structure.md` file. This document is the final, mandatory prerequisite for starting Phase 1.8.

### Phase 1.8: Logic Design & Verification (逻辑设计与验证)
*   **Goal:** Create detailed logic implementation designs that can be directly translated to code, with comprehensive verification of business logic correctness and component interactions.
*   **Scope:** Bridge the gap between structural blueprint (Phase 1.75) and code generation (Phase 2) by providing complete logic implementation specifications.
*   **Human Role:** Review and validate business logic correctness, approve component interaction designs.
*   **LLM Role (Mode 1: Analysis & Design):**
    *   **Create Layered Logic Documentation:** Generate modular logic design documents organized by component and concern.
    *   **Design Component Interactions:** Specify detailed data flows, state transitions, and error propagation between components.
    *   **Verify Logic Completeness:** Ensure all business scenarios from `.spec.md` are covered with complete logic paths.
    *   **Validate Error Handling:** Verify that all error conditions identified in Phase 1 have corresponding logic implementations.
    *   **Cross-Reference Verification:** Ensure consistency between logic designs and Phase 1 implementation patterns.
*   **Output:**
    *   **Primary:** `plans/implementation/logic/` directory with modular logic design documents
    *   **Index:** `plans/implementation/logic/00_logic_overview.md` providing navigation and cross-references
    *   **Verification:** Logic completeness verification report confirming all scenarios are covered

### Phase 2: Code Generation & Implementation (代码生成与实现)
*   **Goal:** Generate executable code by translating the verified logic designs from Phase 1.8 into function-level implementations.
*   **Scope:** Pure translation work - convert detailed logic specifications into working code without creative logic design.
*   **Human Role:** Monitor implementation progress, validate that generated code matches logic specifications.
*   **LLM Role (Mode 2: Execution):**
    *   **Load Logic Specifications:** Reference specific logic design documents from `plans/implementation/logic/` as needed.
    *   **Generate Function Implementations:** Translate logic specifications directly into code, following the exact steps and error handling specified.
    *   **Implement Component Interactions:** Code the data flows and state transitions as specified in the logic designs.
    *   **Apply Implementation Patterns:** Use the error handling, validation, and concurrency patterns documented in Phase 1.
    *   **Incremental Verification:** Test each component against its logic specification as it's implemented.
*   **Output:** Complete, working codebase that implements all logic specifications with verification that each component matches its design.

### Phase 2.5: Functional Completeness & Legacy Integration (功能完整性与遗留系统集成)
*   **Goal:** Ensure implemented functionality is complete, usable, and fully compatible with existing systems. Address gaps between architectural design and functional reality.
*   **Trigger Conditions:**
    *   Phase 2 implementation contains >20% TODO placeholders
    *   Core business logic is incomplete or non-functional
    *   Key services are nil placeholders
    *   Implementation fails to match Phase 1 functional specifications
*   **Scope:** Bridge the gap between compiled code and functional completeness by ensuring real business value delivery.
*   **Human Role:** Validate functional completeness, test actual business scenarios, verify user experience quality.
*   **LLM Role (Mode 1: Analysis & Mode 2: Execution):**
    *   **Mandatory Code Learning:** Deep analysis of legacy code patterns for equivalent functionality
    *   **Functional Gap Analysis:** Compare implementation against Phase 1 specifications for completeness
    *   **Reality-Based Implementation:** Complete missing functionality based on existing code patterns
    *   **Integration Verification:** Ensure new code integrates seamlessly with existing systems
    *   **Business Logic Completion:** Implement core business logic, not just architectural frameworks
    *   **Service Dependency Resolution:** Replace nil placeholders with actual service implementations
*   **Output:** Functionally complete, business-ready implementation that delivers actual user value and integrates with existing systems.

### Phase 3: Representation & Deployment
*   **Goal:** Generate human-readable code (if needed) and deploy the execution body.
*   **Human Role:** Trigger deployment, review generated code for understanding/auditing.
*   **LLM Role (Mode 2: Execution):**
    *   On-demand, generate source code views (e.g., Python, Go) from the internal logic model.
    *   Generate deployment manifests (e.g., Dockerfile, k8s.yaml) based on `deployment.config.toml`.
*   **Output:** Deployed service, and optional, disposable source code files.

### Phase M: Maintenance & Evolution
This phase is triggered when a task involves modifying an existing, deployed system. The Agent MUST first determine the nature of the maintenance task.

#### **Sub-Process M1: Bug Triage & Root Cause Analysis (RCA)**
*   **Trigger:** User reports a bug.
*   **Goal:** Reproduce the bug and find its root cause.
*   **Steps:**
    1.  **Ingest Context:** Load bug report, user-provided logs, relevant `session.log`(s) from original development.
    2.  **Reproduction Attempt:** Propose a test case to reproduce the bug.
    3.  **RCA Loop:** If reproduced, analyze code, logs, and historical context to form a hypothesis. Propose a fix plan.

#### **Sub-Process M2: Change Impact Analysis (强化 1a)**
*   **Trigger:** User requests a modification to a non-trivial existing feature.
*   **Goal:** Understand the full scope and risk of the proposed change **before** implementation.
*   **Steps:**
    1.  **Define Scope:** Clarify the exact change with the user.
    2.  **Run `ANALYZE_IMPACT` command:** (This is a new conceptual command for the bootstrap)
        *   The Agent scans the codebase to build a dependency graph centered on the target component(s).
        *   It identifies all direct and indirect dependencies (callers and callees).
    3.  **Generate Report:** The Agent generates a temporary `impact_analysis_YYYYMMDD.md` report containing:
        *   List of all affected files/modules.
        *   Potential side effects and risks.
        *   A recommended, prioritized list of components to test.
    4.  **Human Review:** The user MUST review and approve this report before a detailed implementation plan is created.

#### **Sub-Process M3: Proactive Refactoring (强化 1b)**
*   **Trigger:** Scheduled task (e.g., nightly) or explicit user command.
*   **Goal:** Identify and propose fixes for technical debt.
*   **Steps:**
    1.  **Scan for Debt:** The Agent scans the codebase, checking for violations of rules in `rules/architecture.md` and `rules/code_style.md` (e.g., complexity, duplication).
    2.  **Log to Ledger:** Identified issues are logged as entries in a new, persistent file: `project_tech_debt_ledger.md`. Each entry includes the issue, location, severity, and the rule violated.
    3.  **Propose Refactoring Plan:** Based on the ledger, the Agent can be prompted to `PROPOSE_PLAN` for refactoring the highest-priority items.

#### **Sub-Process M4: Version Upgrade Assistance (强化 1c)**
*   **Trigger:** User provides a link to a new library/framework version's release notes or migration guide.
*   **Goal:** Semi-automate the process of upgrading dependencies.
*   **Steps:**
    1.  **Ingest Migration Guide:** The Agent reads the provided document.
    2.  **Analyze & Cross-Reference:** It identifies breaking changes, deprecated APIs, and new patterns mentioned in the guide. Then, it scans the project codebase for instances of these patterns.
    3.  **Generate Migration Plan:** The Agent generates a `migration_plan_vX_to_vY.md` containing:
        *   A checklist of all code locations that need modification.
        *   The "before" and "after" code snippets for each change, based on the guide.
        *   A sequence for applying and testing the changes.
    4.  **Execute with Human Supervision:** The Agent executes the plan step-by-step, with human validation at each stage.

### Phase V: Validation & Confidence Loop (验证与信心循环)

*   **Trigger:** This is not a standard, linear phase. It is a special loop that can be initiated by the Human Operator at the end of any phase (typically after a Phase Gate Checklist has been passed) when they express doubt or a lack of confidence in the completeness of the design or implementation.
*   **Goal:** To systematically address human-provided feedback, uncover hidden gaps, and restore confidence before proceeding to the next phase.
*   **LLM Role (Mode 1: Analysis & Mode 4: Adversarial Thinking):**
    1.  **Acknowledge and Formalize Doubt:** The Agent must first acknowledge the validity of the human's concern.
    2.  **Propose a Validation Plan:** The Agent proposes a structured plan to address the doubt. This plan MUST include one or more of the following activities:
        *   **Traceability Matrix Construction:** Create a temporary artifact that maps high-level requirements (from `.intent.yaml`) to low-level specifications (in `.spec.md`) and architectural components.
        *   **Adversarial "Red Team" Analysis:** Proactively challenge the design by asking "what if" questions about reliability, security, and scalability, using `critical_logic_and_risk_assessment.md` as a starting point.
        *   **Edge Case & Concurrent Scenario Generation:** Draft new Gherkin scenarios specifically for complex edge cases and concurrent interactions that may have been missed.
    3.  **Execute and Report:** Upon approval, the Agent executes the validation plan and reports its findings.
*   **Output:** A set of new or updated design/specification artifacts, and a confirmation from the Human Operator that their concerns have been addressed and confidence is restored.
