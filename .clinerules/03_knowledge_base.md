# LeadAgent Knowledge Base (Version 2.2)
# Last Updated 2025-07-02 (UTC)

---
id GO-001
title GORM Atomic Counter Updates
tags [go, gorm, database, concurrency]
summary How to safely update counter fields in GORM to avoid race conditions.
---
For fields like `like_count`, use `gorm.Expr(field_name + , value)` or `gorm.Expr(field_name - , value)` for atomic updates. This translates to a single SQL UPDATE statement (e.g., `UPDATE ... SET field_name = field_name + 1`), preventing read-modify-write race conditions.

---
id TS-001
title React StrictMode Double Invocation
tags [typescript, react, nextjs, debugging]
summary Explains why useEffect may fire twice in development mode.
---
In development (`npm run dev`), React's `StrictMode` intentionally mounts, unmounts, and re-mounts components. This causes `useEffect` to run its setup and cleanup functions twice. This is an expected behavior to help find side-effects and does not occur in production. Do not fix this.

---
id PATTERN-001
title Inter-Bot Linking via Deep Link
tags [telegram, architecture, refactoring]
summary Standard pattern for navigating users between different Telegram bots.
---
Use `https://t.me/<bot_username>?start=<payload>`. The target bot's `start` handler must parse the payload. The bot username should be stored in a configuration file, not hardcoded. (Synthesized from `telegram-bot-refactoring.md`).

---
id TG-001
title Telegram Bot Command Handling
tags [go, telegram, api]
summary How to correctly identify and handle bot commands in Telegram messages.
---
To handle bot commands, check the `Entities` field of the message for an entity with `Type: "bot_command"`. When mocking this for tests, create a `MessageEntity` with `Type: "bot_command"`, `Offset: 0`, and the appropriate `Length`. Avoid using library-specific constants like `tgbotapi.EntityTypeBotCommand` for better portability.

---
id DEBUG-001
title Full-Stack Debugging Strategy
tags [debugging, go, typescript, api]
summary A systematic approach to debugging issues between a Go backend and a JS/TS frontend.
---
When data doesn't flow as expected, log everything. On the backend, log the exact data structure being sent as the API response. On the frontend, log the incoming raw response data *before* it's processed or set into state, and then log the component's state itself *before* rendering. This pinpoints whether the issue is in data transmission, reception, or state management.

---
id TS-002
title Case-Sensitivity in Frontend-Backend Data Binding
tags [typescript, api, debugging]
summary Emphasizes the importance of matching case between backend JSON and frontend TypeScript interfaces.
---
JavaScript/TypeScript object properties are case-sensitive. Frontend TypeScript interfaces (`interface`) must have field names that exactly match the case of the keys in the JSON response from the backend API (e.g., `user_name` in JSON must be `user_name` in the interface, not `userName`). A mismatch will result in `undefined` values.

---
id TS-003
title Using `React.use()` with Next.js Route Params
tags [typescript, react, nextjs]
summary How to correctly handle Promise-based `params` in Next.js using the `React.use()` hook.
---
In modern Next.js, component `params` can be a Promise. To access the resolved value, use the `React.use()` hook (e.g., `const resolvedParams = React.use(params)`). This is the best practice and resolves warnings. The `use` hook must be imported from 'react' and called at the top level of a component or custom hook.

---
id: GO-002
title: GORM 安全更新 JSONB 字段
tags: [go, gorm, database, jsonb]
summary: 介绍在 GORM 中安全更新 JSONB 字段的“读-改-写”模式，以避免数据丢失或并发问题。
---
当需要修改存储在 JSONB 字段中的部分数据时，应遵循“读-改-写”（Read-Modify-Write）模式以确保操作的原子性和数据的完整性。直接使用 `Update` 更新部分 JSON 可能会因并发操作而丢失数据。

**正确模式:**

1.  **读取 (Read):** 从数据库中完整加载包含 JSONB 字段的 GORM 模型对象。
2.  **修改 (Modify):** 在内存中将 JSONB 字段的内容解组（unmarshal）到一个 Go 结构体中。修改该结构体的字段，然后将其重新编组（marshal）为 JSON 字节。
3.  **写入 (Write):** 使用 `db.Model(&YourModel{}).Where(...).Update("jsonb_column_name", ...)` 将新的 JSON 字节完整地写回数据库。

**示例代码 (来自 user_repository.go):**

```go
func (r *gormUserRepository) UpdatePlayerPreferences(ctx context.Context, userID uuid.UUID, prefs models.PlayerPreferences) error {
    db := r.dbClient.GetDB().WithContext(ctx)

    // 1. 读取
    var user models.User
    if err := db.First(&user, "user_id = ?", userID).Error; err != nil {
        return err
    }

    // 2. 修改
    var pd models.PlatformData
    if len(user.PlatformDataJSONB) > 0 {
        _ = json.Unmarshal(user.PlatformDataJSONB, &pd)
    }
    pd.PlayerPreferences = prefs
    bytes, err := json.Marshal(pd)
    if err != nil {
        return err
    }

    // 3. 写入
    return db.Model(&models.User{}).Where("user_id = ?", userID).Update("platform_data_jsonb", datatypes.JSON(bytes)).Error
}
```

---
id: GO-003
title: GORM 中使用事务进行原子性写入
tags: [go, gorm, database, transaction]
summary: 介绍在 GORM 中如何使用事务来确保多个关联数据库操作的原子性，保证数据一致性。
---
当一个业务逻辑需要对多个数据表进行写操作时（例如，创建一个主记录和多个关联的子记录），必须将这些操作包裹在一个数据库事务（Transaction）中。这可以保证所有操作要么全部成功，要么在任何一步失败时全部回滚，从而避免数据处于不一致的状态。

**正确模式:**

1.  **开启事务 (Begin):** 从数据库连接池获取一个事务性句柄。
2.  **执行操作 (Execute):** 使用该事务句柄执行所有的数据库 `Create`, `Update`, `Delete` 操作。
3.  **错误处理与回滚 (Rollback):** 在每个操作后都检查错误。一旦发生错误，立即调用事务的 `Rollback()` 方法，并终止后续操作。
4.  **提交事务 (Commit):** 如果所有操作都成功完成，调用事务的 `Commit()` 方法以永久保存所有更改。

**示例代码 (来自 card_repository.go):**

```go
func (r *gormCardRepository) CreateCardWithArchetypes(ctx context.Context, card *models.Card, archetypes []models.CardCharacterArchetype) error {
    // 1. 开启事务
    txClient, err := r.dbClient.BeginTx(ctx)
    if err != nil {
        return err
    }
    tx := txClient.GetDB()

    // 2. 执行操作 (创建 Card)
    if err := tx.Create(card).Error; err != nil {
        // 3. 错误处理与回滚
        txClient.Rollback()
        return err
    }

    // 2. 执行操作 (循环创建 Archetypes)
    for i := range archetypes {
        archetypes[i].CardID = card.CardID
        if err := tx.Create(&archetypes[i]).Error; err != nil {
            // 3. 错误处理与回滚
            txClient.Rollback()
            return err
        }
    }

    // 4. 提交事务
    if err := txClient.Commit(); err != nil {
        txClient.Rollback()
        return err
    }
    return nil
}

---
id: API-001
title: 前后端API一致性治理
tags: [api, frontend, backend, governance, typescript, go]
summary: 系统性解决前后端API不一致问题的治理方案和最佳实践。
---
当前后端API实现与文档规范不一致时，需要进行系统性治理。核心原则是建立API契约验证机制，确保前后端严格按照统一规范开发。

**问题识别:**
1. API路径不一致（如前端期望 `/v1/cases`，后端实现 `/v1/leads`）
2. 数据模型不匹配（如前端期望 `Case`，后端返回 `AdjudicationLead`）
3. 版本管理缺失（缺少统一的版本前缀）
4. 过时的API调用（调用不存在的端点）

**治理方案:**
1. **API路径常量管理** - 创建 `src/constants/apiPaths.ts` 统一管理所有API路径
2. **类型定义规范** - 在 `src/types/api.ts` 中严格按照后端文档定义类型
3. **API契约验证** - 实现运行时验证，自动检测契约违规
4. **自动化检查工具** - 创建脚本持续监控API一致性

**实施步骤:**
```typescript
// 1. 统一API路径管理
export const CASE_PATHS = {
  CREATE_CASE: '/v1/cases',
  GET_CASES: '/v1/cases',
} as const;

// 2. 数据转换层
const transformApiCase = (apiLead: AdjudicationLead): Case => ({
  id: apiLead.lead_id,
  candidateName: extractCandidateName(apiLead.filename),
  status: mapTaskStatusToCaseStatus(apiLead.status),
  // ...
});

// 3. 契约验证
export const validateApiResponse = <T>(response: any, path: string) => {
  // 验证响应结构...
};
```

**工具链:**
- `scripts/checkApiSync.cjs` - API文档同步检查
- `npm run check:api` - 手动检查命令
- `npm run check:api:watch` - 监控模式
- `npm run generate:api-report` - 生成API报告

---
id: API-002
title: 前后端数据模型适配策略
tags: [api, data-mapping, typescript, go, architecture]
summary: 当前后端数据模型不匹配时的适配策略和转换模式。
---
当前后端使用不同的数据模型时，应创建适配层进行数据转换，而不是强制修改一方的实现。

**适配策略选择:**
1. **前端适配后端** (推荐) - 风险低，不影响后端稳定性
2. **后端实现新API** - 需要额外开发，增加复杂性
3. **双向适配** - 复杂度高，仅在必要时使用

**前端适配模式:**
```typescript
// 后端实际数据结构
interface AdjudicationLead {
  lead_id: string;
  filename: string;
  status: TaskStatus;
  // ...
}

// 前端期望的数据结构
interface Case {
  id: string;
  candidateName: string;
  status: CaseStatus;
  // ...
}

// 转换函数
const transformApiCase = (apiLead: AdjudicationLead): Case => ({
  id: apiLead.lead_id,
  candidateName: extractCandidateName(apiLead.filename),
  status: mapTaskStatusToCaseStatus(apiLead.status),
  // ...
});
```

**状态映射策略:**
```typescript
const mapTaskStatusToCaseStatus = (taskStatus: TaskStatus): CaseStatus => {
  switch (taskStatus) {
    case 'QUEUED': return CaseStatus.COLLECTING;
    case 'PROCESSING': return CaseStatus.PROCESSING;
    case 'COMPLETED': return CaseStatus.COMPLETED;
    case 'FAILED': return CaseStatus.FAILED;
    default: return CaseStatus.COLLECTING;
  }
};
```

**最佳实践:**
1. 在状态管理层进行数据转换，保持组件层的简洁
2. 创建专门的转换函数，便于测试和维护
3. 使用TypeScript类型系统确保转换的正确性
4. 添加错误处理，优雅处理数据不完整的情况

---
id: TOOL-001
title: API治理工具链设计
tags: [tools, automation, api, governance, nodejs, typescript]
summary: 构建自动化API治理工具链的设计原则和实现方法。
---
API治理需要自动化工具支持，确保前后端规范的持续一致性。

**工具链组成:**
1. **API同步检查器** - 扫描前端API调用，对比后端文档规范
2. **契约验证器** - 运行时验证API响应结构
3. **报告生成器** - 生成API文档对比报告
4. **监控工具** - 持续监控API一致性

**检查器实现模式:**
```javascript
// 提取后端API端点
const extractApiEndpoints = (specContent) => {
  const endpointRegex = /(?:POST|GET|PUT|DELETE)\s+([\/\w\-\{\}]+)/g;
  // ...
};

// 扫描前端API调用
const extractFrontendApiCalls = (sourceDir) => {
  const apiCallRegex = /(?:apiClient\.|axios\.)(?:get|post|put|delete)\s*\(\s*['"`]([^'"`]+)['"`]/g;
  // ...
};

// 检查一致性
const checkApiPathConsistency = (backendEndpoints, frontendCalls) => {
  // 对比逻辑...
};
```

**集成到开发流程:**
```json
{
  "scripts": {
    "check:api": "node scripts/checkApiSync.cjs",
    "check:api:watch": "nodemon --watch specs --watch src --ext md,ts,tsx --exec \"npm run check:api\"",
    "validate:types": "tsc --noEmit --skipLibCheck",
    "generate:api-report": "node scripts/generateApiReport.cjs"
  }
}
```

**CI/CD集成:**
- 在代码审查阶段运行API检查
- 阻止不符合规范的代码合并
- 自动生成API变更报告

**监控指标:**
- API路径一致性率
- 类型覆盖率
- 文档同步率
- 契约违规次数

---
id: DB-001
title: 数据库参数占位符兼容性
tags: [database, go, sqlite, postgresql, compatibility]
summary: 解决SQLite与PostgreSQL参数占位符不兼容导致的数据库更新失败问题。
---
当应用需要支持多种数据库时，必须处理不同数据库的参数占位符语法差异。PostgreSQL使用 `$1, $2, $3` 格式，而SQLite使用 `?, ?, ?` 格式。

**问题现象:**
- UPDATE操作返回 `rows affected: 0`
- 数据库操作看似成功但实际未更新任何记录
- 参数传递正确但SQL执行无效果

**根本原因:**
```go
// 错误：在SQLite数据库上使用PostgreSQL语法
query := `UPDATE table SET col1 = $1, col2 = $2 WHERE id = $3`
result, err := db.ExecContext(ctx, query, value1, value2, id)
// SQLite无法识别$1, $2, $3占位符，导致更新失败
```

**解决方案:**
```go
// 方案1：根据数据库类型动态构建查询
func (r *Repository) buildUpdateQuery(driver string) string {
    if driver == "postgres" {
        return `UPDATE table SET col1 = $1, col2 = $2 WHERE id = $3`
    }
    return `UPDATE table SET col1 = ?, col2 = ? WHERE id = ?`
}

// 方案2：统一使用SQLite语法（兼容性更好）
query := `UPDATE table SET col1 = ?, col2 = ? WHERE id = ?`
result, err := db.ExecContext(ctx, query, value1, value2, id)
```

**最佳实践:**
1. 在数据库抽象层统一处理占位符差异
2. 确保参数传递顺序与占位符位置匹配
3. 添加数据库兼容性集成测试
4. 检查 `result.RowsAffected()` 验证操作成功

---
id: WORKER-001
title: Worker错误处理与业务状态区分
tags: [worker, go, error-handling, redis, queue]
summary: 优化Worker服务的错误处理逻辑，正确区分系统错误和正常业务状态。
---
Worker服务在处理任务队列时，必须正确区分系统错误和正常业务状态，避免将空队列等正常情况误报为错误。

**问题现象:**
- 空队列时产生大量错误日志
- `redis: nil` 被当作系统故障处理
- 正常业务状态触发错误告警

**错误处理分类:**
```go
// 1. 系统错误 - 需要记录和告警
type DatabaseConnectionError struct{ Cause error }
type NetworkTimeoutError struct{ Cause error }

// 2. 业务条件 - 正常状态，无需错误日志
type NoTaskAvailableError struct{}
type QueueEmptyError struct{}

// 3. 可恢复错误 - 需要重试机制
type TemporaryServiceError struct{ Service string; Cause error }
```

**类型安全的错误检测:**
```go
// 推荐：使用errors.As()进行类型安全检测
var noTaskErr *NoTaskAvailableError
if errors.As(err, &noTaskErr) {
    // 空队列是正常业务状态，不记录错误
    return nil
}

var tempErr *TemporaryServiceError
if errors.As(err, &tempErr) {
    // 实现重试逻辑
    return r.retryWithBackoff(tempErr.Service)
}

// 避免：字符串匹配检测（脆弱且易错）
if err.Error() == "redis: nil" {
    // 不推荐的做法
}
```

**Redis空队列处理:**
```go
func (tsm *TaskStateManager) AcquireTask(ctx context.Context, workerID string) (*Task, error) {
    result, err := tsm.redis.Eval(ctx, script, []string{queueKey}, workerID).Result()
    if err != nil {
        // 检查Redis返回nil的情况（队列为空）
        if err.Error() == "redis: nil" {
            return nil, &NoTaskAvailableError{}
        }
        return nil, fmt.Errorf("failed to acquire task: %w", err)
    }
    // 处理正常任务...
}
```

**日志记录策略:**
```go
// 系统错误：ERROR级别
if isSystemError(err) {
    logger.WithError(err).Error("System failure occurred")
}

// 业务条件：DEBUG级别或不记录
if isBusinessCondition(err) {
    logger.Debug("Normal business condition")
}

// 可恢复错误：WARN级别
if isRecoverableError(err) {
    logger.WithError(err).Warn("Recoverable error, will retry")
}
```

---
id: GO-004
title: Go错误处理最佳实践
tags: [go, error-handling, types, patterns]
summary: Go语言中类型安全错误处理的最佳实践和常见模式。
---
Go语言的错误处理应该使用类型安全的方法，避免字符串匹配等脆弱的检测方式。

**自定义错误类型:**
```go
// 定义具体的错误类型
type ValidationError struct {
    Field   string
    Message string
}

func (e *ValidationError) Error() string {
    return fmt.Sprintf("validation failed for %s: %s", e.Field, e.Message)
}

// 包装错误以保持错误链
type ServiceError struct {
    Service string
    Cause   error
}

func (e *ServiceError) Error() string {
    return fmt.Sprintf("service %s failed: %v", e.Service, e.Cause)
}

func (e *ServiceError) Unwrap() error {
    return e.Cause
}
```

**类型安全的错误检测:**
```go
// 使用errors.As()检测特定错误类型
var validationErr *ValidationError
if errors.As(err, &validationErr) {
    // 处理验证错误
    log.Printf("Validation failed for field: %s", validationErr.Field)
}

// 使用errors.Is()检测特定错误值
if errors.Is(err, context.DeadlineExceeded) {
    // 处理超时错误
    return handleTimeout()
}

// 检查错误链中的特定类型
var serviceErr *ServiceError
if errors.As(err, &serviceErr) {
    // 可以访问包装的原始错误
    log.Printf("Service %s failed: %v", serviceErr.Service, serviceErr.Cause)
}
```

**错误包装和传播:**
```go
// 使用fmt.Errorf的%w动词包装错误
func processData(data []byte) error {
    if err := validateData(data); err != nil {
        return fmt.Errorf("failed to process data: %w", err)
    }
    return nil
}

// 创建错误链
func handleRequest() error {
    if err := processData(data); err != nil {
        return fmt.Errorf("request handling failed: %w", err)
    }
    return nil
}
```

**错误分类和处理策略:**
```go
// 根据错误类型采取不同的处理策略
func handleError(err error) {
    var validationErr *ValidationError
    var timeoutErr *TimeoutError
    var systemErr *SystemError

    switch {
    case errors.As(err, &validationErr):
        // 用户输入错误，返回400
        respondWithBadRequest(validationErr.Message)
    case errors.As(err, &timeoutErr):
        // 超时错误，返回504
        respondWithTimeout()
    case errors.As(err, &systemErr):
        // 系统错误，记录日志并返回500
        log.Error("System error occurred", "error", err)
        respondWithInternalError()
    default:
        // 未知错误，记录并返回通用错误
        log.Error("Unknown error occurred", "error", err)
        respondWithInternalError()
    }
}
```

---
id: SECURITY-001
title: 安全渗透测试框架实施
tags: [security, testing, go, penetration-testing]
summary: 基于 Resumix 项目的综合安全渗透测试框架实施经验
---
**安全渗透测试核心组件:**

**1. 速率限制验证:**
- 配置可调节限制 (10 req/s, burst 5) 验证强制执行
- 测试需要考虑突发限制对测试成功率的影响
- 审计日志记录所有违规行为，使用结构化 JSON 格式

**2. 输入验证测试:**
- MVP 阶段记录当前行为而非强制严格阻断
- 生产环境需要更严格的输入过滤策略
- URL 参数注入需要适当编码避免 HTTP 请求解析失败

**3. 安全头验证:**
- 期望值必须与实际中间件实现完全匹配
- CSP 策略需要详细配置，HSTS 需要 preload 指令
- X-Frame-Options, X-XSS-Protection 等标准安全头

**4. SQL 注入防护:**
- 使用 URL 编码进行负载测试避免 HTTP 解析问题
- 参数化查询和安全的参数处理
- 测试各种注入向量包括联合查询、布尔盲注等

**5. XSS 防护测试:**
- 测试全面的负载向量：脚本标签、事件处理器、SVG、iframe
- 验证输出编码和内容安全策略的有效性
- 测试反射型、存储型和 DOM 型 XSS

**关键测试洞察:**
- 安全测试应记录当前行为而非在 MVP 阶段强制严格阻断
- 中间件顺序显著影响性能 - 将昂贵的中间件放在最后
- 安全头值必须与实际中间件实现完全匹配

---
id: PERFORMANCE-001
title: 性能基准测试框架
tags: [performance, benchmarking, go, optimization]
summary: 基于 Resumix 项目的综合性能基准测试框架经验
---
**性能基准测试核心组件:**

**1. HTTP 中间件基准测试:**
- 速率限制中间件导致 27 倍性能影响
- 中间件顺序对性能有显著影响
- 内存分配监控对垃圾回收影响评估

**2. 缓存性能测试:**
- Redis 实现 34,426 ops/sec，命中率 80%+
- 缓存命中率超过 80% 表明有效的缓存策略
- 并发操作测试验证缓存一致性

**3. 数据库性能测试:**
- 需要 PostgreSQL 配置进行综合测试
- 连接池设置优化 (25 最大连接，5 空闲)
- 查询性能监控和慢查询识别

**4. 并发操作测试:**
- 系统在适当的中间件排序下处理并发请求良好
- 基准测试隔离需要独立的测试数据库和 Redis 实例
- 负载测试验证安全控制在并发负载下的有效性

**性能优化洞察:**
- 中间件排序显著影响性能 - 将昂贵的中间件放在最后
- 缓存命中率超过 80% 表明有效的缓存策略
- 每操作内存分配应监控垃圾回收影响
- 基准测试隔离需要独立的测试数据库和 Redis 实例

---
id: BACKEND-001
title: 后端代码审查标准
tags: [backend, code-review, go, standards]
summary: 基于 Resumix 项目的系统化后端代码审查标准和流程
---
**系统化审查流程:**

**1. 数据库架构一致性:**
- 验证多表设计与文档化架构匹配
- 检查迁移策略与文档化多表架构的对齐
- 确保数据库模式与业务逻辑需求一致

**2. 安全中间件集成:**
- 确保所有端点使用完整的安全中间件栈
- 验证安全中间件函数签名和参数要求
- 检查审计日志集成与结构化 JSON 格式

**3. 错误处理标准化:**
- 实施跨所有端点的一致错误响应格式
- 标准化错误代码和消息结构
- 确保适当的错误日志记录和监控

**4. 性能监控:**
- 添加生产监控的综合指标收集
- 实施性能基准测试和监控
- 配置告警和性能阈值

**5. 代码质量标准:**
- 强制一致的命名、文档和结构模式
- Redis 版本兼容性检查 (v8 vs v9 迁移模式)
- 依赖管理和版本控制标准

**关键审查领域:**
- 数据库迁移策略与文档化多表架构的对齐
- 安全中间件函数签名和参数要求
- Redis 版本兼容性跨代码库 (v8 vs v9 迁移模式)
- 审计日志集成与结构化 JSON 格式和唯一事件 ID

---
id: DEPLOYMENT-001
title: 生产部署准备框架
tags: [deployment, production, monitoring, security]
summary: 基于 Resumix 项目的综合生产部署准备框架和最佳实践
---
**生产部署核心组件:**

**1. 环境配置管理:**
- 生产环境变量模板与安全密钥生成指南
- 数据库连接池优化 (25 最大连接，5 空闲)
- Redis 配置与连接池设置
- SSL/TLS 证书配置和 HTTPS 强制

**2. 监控和指标收集:**
- Prometheus 指标收集配置
- 告警规则覆盖应用、数据库、系统、网络层面
- Grafana 仪表板配置
- 健康检查端点和服务状态监控

**3. 部署脚本自动化:**
- 系统化部署脚本支持干运行模式
- 数据库迁移和备份策略
- 服务管理和 systemd 配置
- 回滚程序和故障恢复

**4. 安全配置:**
- Nginx 反向代理配置与安全头
- 速率限制和连接限制
- 防火墙规则和网络安全
- 审计日志和安全事件监控

**5. 性能优化:**
- 中间件排序优化
- 缓存策略和 Redis 配置
- 数据库查询优化和索引管理
- 资源限制和扩展策略

**部署最佳实践:**
- 分阶段部署：测试 → 预生产 → 生产
- 自动化测试验证：单元测试 → 集成测试 → 安全测试
- 监控驱动部署：指标收集 → 告警配置 → 性能基准
- 安全优先原则：最小权限 → 深度防御 → 持续监控
