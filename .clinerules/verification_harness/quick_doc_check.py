#!/usr/bin/env python3
"""
Quick Documentation Alignment Check v1.0
快速文档对齐检查工具

Purpose: 快速检查关键的文档-代码一致性问题
Usage: python3 quick_doc_check.py <project_root>
"""

import os
import sys
import re
from pathlib import Path
from typing import Set, List, Dict

def scan_go_api_endpoints(project_root: Path) -> Set[str]:
    """扫描Go代码中的API端点"""
    endpoints = set()
    
    # 只扫描主要的API文件
    api_files = [
        project_root / "cmd" / "resumix-api" / "main.go",
        project_root / "internal" / "api" / "router.go",
        project_root / "internal" / "api" / "handler.go"
    ]
    
    for api_file in api_files:
        if api_file.exists():
            try:
                content = api_file.read_text(encoding='utf-8')
                
                # 匹配Gin路由定义
                patterns = [
                    r'\.GET\s*\(\s*["\']([^"\']+)["\']',
                    r'\.POST\s*\(\s*["\']([^"\']+)["\']',
                    r'\.PUT\s*\(\s*["\']([^"\']+)["\']',
                    r'\.DELETE\s*\(\s*["\']([^"\']+)["\']'
                ]
                
                for pattern in patterns:
                    method = pattern.split('\\')[1].split('\\')[0]
                    matches = re.findall(pattern, content)
                    for match in matches:
                        endpoints.add(f"{method} {match}")
                        
            except Exception as e:
                print(f"Warning: Could not read {api_file}: {e}")
    
    return endpoints

def scan_documented_endpoints(project_root: Path) -> Set[str]:
    """扫描文档中的API端点"""
    endpoints = set()
    
    # 检查主要文档文件
    doc_files = [
        project_root / "resumix_mvp.md",
        project_root / "resume_app" / "docs" / "api-integration.md"
    ]
    
    for doc_file in doc_files:
        if doc_file.exists():
            try:
                content = doc_file.read_text(encoding='utf-8')
                
                # 匹配API端点文档
                patterns = [
                    r'`(GET|POST|PUT|DELETE)\s+([^`]+)`',
                    r'\*\*`(GET|POST|PUT|DELETE)\s+([^`]+)`\*\*'
                ]
                
                for pattern in patterns:
                    matches = re.findall(pattern, content)
                    for method, path in matches:
                        endpoints.add(f"{method} {path}")
                        
            except Exception as e:
                print(f"Warning: Could not read {doc_file}: {e}")
    
    return endpoints

def scan_frontend_api_calls(project_root: Path) -> Set[str]:
    """扫描前端API调用"""
    api_calls = set()
    
    # 只检查主要的前端文件
    frontend_files = []
    resume_app_dir = project_root / "resume_app"
    
    if resume_app_dir.exists():
        # 只扫描src目录下的主要文件
        src_dir = resume_app_dir / "src"
        if src_dir.exists():
            for pattern in ["*.vue", "*.js", "*.ts"]:
                frontend_files.extend(src_dir.rglob(pattern))
    
    # 限制文件数量以避免超时
    frontend_files = frontend_files[:20]
    
    for file in frontend_files:
        try:
            content = file.read_text(encoding='utf-8')
            
            # 匹配API调用
            patterns = [
                r'fetch\s*\(\s*["`\']([^"`\']+)["`\']',
                r'axios\.\w+\s*\(\s*["`\']([^"`\']+)["`\']'
            ]
            
            for pattern in patterns:
                matches = re.findall(pattern, content)
                for match in matches:
                    if match.startswith('/'):
                        api_calls.add(match)
                        
        except Exception:
            continue
    
    return api_calls

def scan_env_variables(project_root: Path) -> tuple[Set[str], Set[str]]:
    """扫描环境变量使用情况"""
    used_vars = set()
    documented_vars = set()
    
    # 扫描Go代码中的环境变量
    go_files = list(project_root.rglob("*.go"))[:10]  # 限制文件数量
    
    for go_file in go_files:
        try:
            content = go_file.read_text(encoding='utf-8')
            matches = re.findall(r'os\.Getenv\s*\(\s*["\']([^"\']+)["\']', content)
            used_vars.update(matches)
        except Exception:
            continue
    
    # 检查.env.example
    env_example = project_root / ".env.example"
    if env_example.exists():
        try:
            content = env_example.read_text(encoding='utf-8')
            matches = re.findall(r'^([A-Z_][A-Z0-9_]*)\s*=', content, re.MULTILINE)
            documented_vars.update(matches)
        except Exception:
            pass
    
    return used_vars, documented_vars

def main():
    if len(sys.argv) != 2:
        print("Usage: python3 quick_doc_check.py <project_root>")
        sys.exit(1)
    
    project_root = Path(sys.argv[1])
    
    print("🔍 Quick Documentation Alignment Check")
    print("=" * 50)
    
    issues = []
    warnings = []
    
    # 1. 检查API端点
    print("📡 Checking API endpoints...")
    go_endpoints = scan_go_api_endpoints(project_root)
    doc_endpoints = scan_documented_endpoints(project_root)
    
    undocumented = go_endpoints - doc_endpoints
    obsolete = doc_endpoints - go_endpoints
    
    if undocumented:
        issues.append(f"Undocumented API endpoints: {undocumented}")
    
    if obsolete:
        warnings.append(f"Obsolete documented endpoints: {obsolete}")
    
    # 2. 检查前端API调用
    print("🔄 Checking frontend API calls...")
    frontend_calls = scan_frontend_api_calls(project_root)
    backend_paths = {ep.split(' ', 1)[1] for ep in go_endpoints if ' ' in ep}
    
    mismatched = frontend_calls - backend_paths
    if mismatched:
        issues.append(f"Frontend calls non-existent APIs: {mismatched}")
    
    # 3. 检查环境变量
    print("⚙️ Checking environment variables...")
    used_vars, documented_vars = scan_env_variables(project_root)
    
    undoc_vars = used_vars - documented_vars
    unused_vars = documented_vars - used_vars
    
    if undoc_vars:
        issues.append(f"Undocumented environment variables: {undoc_vars}")
    
    if unused_vars:
        warnings.append(f"Unused documented variables: {unused_vars}")
    
    # 生成报告
    print("\n" + "=" * 50)
    print("📋 QUICK CHECK RESULTS")
    print("=" * 50)
    
    if not issues and not warnings:
        print("✅ No major alignment issues found!")
        status = "PASS"
    elif issues:
        print(f"❌ Found {len(issues)} critical issues:")
        for issue in issues:
            print(f"  • {issue}")
        status = "FAIL"
    else:
        print(f"⚠️ Found {len(warnings)} warnings:")
        for warning in warnings:
            print(f"  • {warning}")
        status = "PARTIAL"
    
    print(f"\n🎯 Status: {status}")
    print(f"📊 API endpoints in code: {len(go_endpoints)}")
    print(f"📚 API endpoints documented: {len(doc_endpoints)}")
    print(f"🔗 Frontend API calls: {len(frontend_calls)}")
    print(f"🔧 Environment variables used: {len(used_vars)}")
    print(f"📝 Environment variables documented: {len(documented_vars)}")
    
    # 详细信息
    if go_endpoints:
        print(f"\n📡 Found API endpoints in code:")
        for ep in sorted(go_endpoints):
            print(f"  • {ep}")
    
    if doc_endpoints:
        print(f"\n📚 Found documented endpoints:")
        for ep in sorted(doc_endpoints):
            print(f"  • {ep}")
    
    if frontend_calls:
        print(f"\n🔗 Found frontend API calls:")
        for call in sorted(frontend_calls):
            print(f"  • {call}")
    
    sys.exit(0 if status == "PASS" else 1)

if __name__ == "__main__":
    main()
