#!/usr/bin/env python3
"""
Documentation Alignment Checker v1.0
强制性文档对齐检查工具

Purpose: 确保代码实现与文档规范保持一致，防止实现过程中的概念混淆
Usage: python3 doc_alignment_checker.py <project_root>
"""

import os
import sys
import json
import yaml
import re
from pathlib import Path
from typing import Dict, List, Set, Tuple, Optional

class DocumentationAlignmentChecker:
    def __init__(self, project_root: str):
        self.project_root = Path(project_root)
        self.issues = []
        self.warnings = []
        self.api_endpoints = set()
        self.documented_endpoints = set()
        
    def check_alignment(self) -> Dict:
        """执行完整的文档对齐检查"""
        print("🔍 Starting Documentation Alignment Check...")

        try:
            # 1. 检查API端点一致性
            print("📡 Checking API endpoint alignment...")
            self._check_api_endpoints()

            # 2. 检查配置文件一致性
            print("⚙️ Checking configuration consistency...")
            self._check_configuration_consistency()

            # 3. 检查前端-后端接口一致性
            print("🔄 Checking frontend-backend alignment...")
            self._check_frontend_backend_alignment()

        except Exception as e:
            print(f"❌ Error during check: {e}")
            self.issues.append({
                "type": "CHECK_ERROR",
                "severity": "HIGH",
                "message": f"Error during alignment check: {str(e)}",
                "details": str(e)
            })

        # 生成报告
        return self._generate_report()
    
    def _check_api_endpoints(self):
        """检查API端点文档与实现的一致性"""
        print("📡 Checking API endpoint alignment...")
        
        # 扫描Go代码中的API端点
        self._scan_go_api_endpoints()
        
        # 扫描文档中的API端点
        self._scan_documented_endpoints()
        
        # 比较差异
        undocumented = self.api_endpoints - self.documented_endpoints
        obsolete_docs = self.documented_endpoints - self.api_endpoints
        
        if undocumented:
            self.issues.append({
                "type": "UNDOCUMENTED_ENDPOINTS",
                "severity": "HIGH",
                "message": f"Found {len(undocumented)} API endpoints in code but not in documentation",
                "details": list(undocumented)
            })
        
        if obsolete_docs:
            self.warnings.append({
                "type": "OBSOLETE_DOCUMENTATION",
                "severity": "MEDIUM", 
                "message": f"Found {len(obsolete_docs)} documented endpoints not in current code",
                "details": list(obsolete_docs)
            })
    
    def _scan_go_api_endpoints(self):
        """扫描Go代码中的API端点定义"""
        go_files = list(self.project_root.rglob("*.go"))
        
        for go_file in go_files:
            try:
                content = go_file.read_text(encoding='utf-8')
                
                # 匹配Gin路由定义
                patterns = [
                    r'\.GET\s*\(\s*["\']([^"\']+)["\']',
                    r'\.POST\s*\(\s*["\']([^"\']+)["\']',
                    r'\.PUT\s*\(\s*["\']([^"\']+)["\']',
                    r'\.DELETE\s*\(\s*["\']([^"\']+)["\']',
                    r'\.PATCH\s*\(\s*["\']([^"\']+)["\']'
                ]
                
                for pattern in patterns:
                    matches = re.findall(pattern, content)
                    for match in matches:
                        method = pattern.split('\\')[1].split('\\')[0]
                        endpoint = f"{method} {match}"
                        self.api_endpoints.add(endpoint)
                        
            except Exception as e:
                self.warnings.append({
                    "type": "FILE_READ_ERROR",
                    "severity": "LOW",
                    "message": f"Could not read Go file: {go_file}",
                    "details": str(e)
                })
    
    def _scan_documented_endpoints(self):
        """扫描文档中的API端点定义"""
        doc_files = [
            self.project_root / "resumix_mvp.md",
            self.project_root / "plans" / "specs" / "api_specification.md",
            self.project_root / "docs" / "api.md"
        ]
        
        for doc_file in doc_files:
            if doc_file.exists():
                try:
                    content = doc_file.read_text(encoding='utf-8')
                    
                    # 匹配API端点文档
                    patterns = [
                        r'\*\*`(GET|POST|PUT|DELETE|PATCH)\s+([^`]+)`\*\*',
                        r'`(GET|POST|PUT|DELETE|PATCH)\s+([^`]+)`',
                        r'(GET|POST|PUT|DELETE|PATCH)\s+`([^`]+)`',
                        r'(GET|POST|PUT|DELETE|PATCH)\s+/([^\s\n]+)'
                    ]
                    
                    for pattern in patterns:
                        matches = re.findall(pattern, content)
                        for match in matches:
                            if len(match) == 2:
                                method, path = match
                                endpoint = f"{method} /{path.lstrip('/')}"
                                self.documented_endpoints.add(endpoint)
                                
                except Exception as e:
                    self.warnings.append({
                        "type": "DOC_READ_ERROR",
                        "severity": "LOW",
                        "message": f"Could not read documentation file: {doc_file}",
                        "details": str(e)
                    })
    
    def _check_data_models(self):
        """检查数据模型文档与实现的一致性"""
        print("🗃️ Checking data model alignment...")
        
        # 扫描Go结构体定义
        go_structs = self._scan_go_structs()
        
        # 扫描数据库schema
        db_tables = self._scan_database_schema()
        
        # 检查一致性
        self._validate_struct_table_alignment(go_structs, db_tables)
    
    def _scan_go_structs(self) -> Dict[str, List[str]]:
        """扫描Go代码中的结构体定义"""
        structs = {}
        go_files = list(self.project_root.rglob("*.go"))
        
        for go_file in go_files:
            try:
                content = go_file.read_text(encoding='utf-8')
                
                # 匹配结构体定义
                struct_pattern = r'type\s+(\w+)\s+struct\s*\{([^}]+)\}'
                matches = re.findall(struct_pattern, content, re.DOTALL)
                
                for struct_name, struct_body in matches:
                    fields = []
                    field_pattern = r'(\w+)\s+([^\n`]+)(?:`[^`]*`)?'
                    field_matches = re.findall(field_pattern, struct_body)
                    
                    for field_name, field_type in field_matches:
                        fields.append(f"{field_name}:{field_type.strip()}")
                    
                    structs[struct_name] = fields
                    
            except Exception as e:
                continue
                
        return structs
    
    def _scan_database_schema(self) -> Dict[str, List[str]]:
        """扫描数据库schema定义"""
        tables = {}
        
        # 扫描SQL文件
        sql_files = list(self.project_root.rglob("*.sql"))
        
        for sql_file in sql_files:
            try:
                content = sql_file.read_text(encoding='utf-8')
                
                # 匹配CREATE TABLE语句
                table_pattern = r'CREATE\s+TABLE\s+(\w+)\s*\(([^;]+)\);'
                matches = re.findall(table_pattern, content, re.DOTALL | re.IGNORECASE)
                
                for table_name, table_body in matches:
                    columns = []
                    # 简化的列定义匹配
                    column_pattern = r'(\w+)\s+([^\n,]+)'
                    column_matches = re.findall(column_pattern, table_body)
                    
                    for column_name, column_def in column_matches:
                        if not column_name.upper() in ['PRIMARY', 'FOREIGN', 'UNIQUE', 'INDEX', 'CONSTRAINT']:
                            columns.append(f"{column_name}:{column_def.strip()}")
                    
                    tables[table_name] = columns
                    
            except Exception as e:
                continue
                
        return tables
    
    def _validate_struct_table_alignment(self, structs: Dict, tables: Dict):
        """验证结构体与数据库表的对齐"""
        # 这里可以添加更复杂的对齐检查逻辑
        # 目前只做基本的存在性检查
        
        for struct_name in structs:
            # 尝试找到对应的表名（可能需要转换命名规则）
            possible_table_names = [
                struct_name.lower(),
                self._camel_to_snake(struct_name),
                f"{self._camel_to_snake(struct_name)}s"  # 复数形式
            ]
            
            found_table = False
            for table_name in possible_table_names:
                if table_name in tables:
                    found_table = True
                    break
            
            if not found_table:
                self.warnings.append({
                    "type": "STRUCT_WITHOUT_TABLE",
                    "severity": "MEDIUM",
                    "message": f"Go struct '{struct_name}' has no corresponding database table",
                    "details": f"Checked table names: {possible_table_names}"
                })
    
    def _camel_to_snake(self, name: str) -> str:
        """将驼峰命名转换为下划线命名"""
        s1 = re.sub('(.)([A-Z][a-z]+)', r'\1_\2', name)
        return re.sub('([a-z0-9])([A-Z])', r'\1_\2', s1).lower()
    
    def _check_configuration_consistency(self):
        """检查配置文件一致性"""
        print("⚙️ Checking configuration consistency...")
        
        # 检查.env.example与代码中使用的环境变量
        self._check_env_variables()
    
    def _check_env_variables(self):
        """检查环境变量的一致性"""
        # 扫描代码中使用的环境变量
        used_vars = set()
        go_files = list(self.project_root.rglob("*.go"))
        
        for go_file in go_files:
            try:
                content = go_file.read_text(encoding='utf-8')
                
                # 匹配os.Getenv调用
                env_pattern = r'os\.Getenv\s*\(\s*["\']([^"\']+)["\']'
                matches = re.findall(env_pattern, content)
                used_vars.update(matches)
                
            except Exception:
                continue
        
        # 检查.env.example文件
        env_example_file = self.project_root / ".env.example"
        documented_vars = set()
        
        if env_example_file.exists():
            try:
                content = env_example_file.read_text(encoding='utf-8')
                
                # 匹配环境变量定义
                var_pattern = r'^([A-Z_][A-Z0-9_]*)\s*='
                matches = re.findall(var_pattern, content, re.MULTILINE)
                documented_vars.update(matches)
                
            except Exception:
                pass
        
        # 检查差异
        undocumented_vars = used_vars - documented_vars
        unused_vars = documented_vars - used_vars
        
        if undocumented_vars:
            self.issues.append({
                "type": "UNDOCUMENTED_ENV_VARS",
                "severity": "HIGH",
                "message": f"Found {len(undocumented_vars)} environment variables used in code but not in .env.example",
                "details": list(undocumented_vars)
            })
        
        if unused_vars:
            self.warnings.append({
                "type": "UNUSED_ENV_VARS",
                "severity": "LOW",
                "message": f"Found {len(unused_vars)} environment variables in .env.example but not used in code",
                "details": list(unused_vars)
            })
    
    def _check_frontend_backend_alignment(self):
        """检查前端后端接口对齐"""
        print("🔄 Checking frontend-backend alignment...")
        
        # 扫描前端API调用
        frontend_apis = self._scan_frontend_api_calls()
        
        # 与后端API端点比较
        backend_endpoints = {ep.split(' ', 1)[1] for ep in self.api_endpoints}
        
        for api_call in frontend_apis:
            if api_call not in backend_endpoints:
                self.issues.append({
                    "type": "FRONTEND_API_MISMATCH",
                    "severity": "HIGH",
                    "message": f"Frontend calls API endpoint that doesn't exist in backend",
                    "details": api_call
                })
    
    def _scan_frontend_api_calls(self) -> Set[str]:
        """扫描前端代码中的API调用"""
        api_calls = set()
        
        # 扫描Vue/JS文件
        frontend_files = []
        frontend_dirs = [
            self.project_root / "resume_app",
            self.project_root / "frontend",
            self.project_root / "web"
        ]
        
        for frontend_dir in frontend_dirs:
            if frontend_dir.exists():
                frontend_files.extend(frontend_dir.rglob("*.vue"))
                frontend_files.extend(frontend_dir.rglob("*.js"))
                frontend_files.extend(frontend_dir.rglob("*.ts"))
        
        for file in frontend_files:
            try:
                content = file.read_text(encoding='utf-8')
                
                # 匹配API调用
                patterns = [
                    r'fetch\s*\(\s*["`\']([^"`\']+)["`\']',
                    r'axios\.\w+\s*\(\s*["`\']([^"`\']+)["`\']',
                    r'\.get\s*\(\s*["`\']([^"`\']+)["`\']',
                    r'\.post\s*\(\s*["`\']([^"`\']+)["`\']'
                ]
                
                for pattern in patterns:
                    matches = re.findall(pattern, content)
                    for match in matches:
                        if match.startswith('/'):
                            api_calls.add(match)
                            
            except Exception:
                continue
        
        return api_calls
    
    def _generate_report(self) -> Dict:
        """生成检查报告"""
        total_issues = len(self.issues)
        total_warnings = len(self.warnings)
        
        status = "PASS"
        if total_issues > 0:
            status = "FAIL"
        elif total_warnings > 0:
            status = "PARTIAL"
        
        report = {
            "status": status,
            "summary": {
                "total_issues": total_issues,
                "total_warnings": total_warnings,
                "api_endpoints_found": len(self.api_endpoints),
                "documented_endpoints_found": len(self.documented_endpoints)
            },
            "issues": self.issues,
            "warnings": self.warnings,
            "recommendations": self._generate_recommendations()
        }
        
        return report
    
    def _generate_recommendations(self) -> List[str]:
        """生成改进建议"""
        recommendations = []
        
        if any(issue["type"] == "UNDOCUMENTED_ENDPOINTS" for issue in self.issues):
            recommendations.append("Update API documentation to include all implemented endpoints")
        
        if any(issue["type"] == "FRONTEND_API_MISMATCH" for issue in self.issues):
            recommendations.append("Align frontend API calls with backend implementation")
        
        if any(issue["type"] == "UNDOCUMENTED_ENV_VARS" for issue in self.issues):
            recommendations.append("Update .env.example with all required environment variables")
        
        return recommendations

def main():
    if len(sys.argv) != 2:
        print("Usage: python3 doc_alignment_checker.py <project_root>")
        sys.exit(1)
    
    project_root = sys.argv[1]
    checker = DocumentationAlignmentChecker(project_root)
    
    try:
        report = checker.check_alignment()
        
        # 输出报告
        print("\n" + "="*60)
        print("📋 DOCUMENTATION ALIGNMENT REPORT")
        print("="*60)
        
        print(f"\n🎯 Status: {report['status']}")
        print(f"📊 Issues: {report['summary']['total_issues']}")
        print(f"⚠️  Warnings: {report['summary']['total_warnings']}")
        
        if report['issues']:
            print(f"\n❌ CRITICAL ISSUES:")
            for issue in report['issues']:
                print(f"  • {issue['type']}: {issue['message']}")
                if issue.get('details'):
                    print(f"    Details: {issue['details']}")
        
        if report['warnings']:
            print(f"\n⚠️  WARNINGS:")
            for warning in report['warnings']:
                print(f"  • {warning['type']}: {warning['message']}")
        
        if report['recommendations']:
            print(f"\n💡 RECOMMENDATIONS:")
            for rec in report['recommendations']:
                print(f"  • {rec}")
        
        print("\n" + "="*60)
        
        # 保存详细报告
        report_file = Path(project_root) / "doc_alignment_report.json"
        with open(report_file, 'w') as f:
            json.dump(report, f, indent=2)
        
        print(f"📄 Detailed report saved to: {report_file}")
        
        # 退出码
        sys.exit(0 if report['status'] == 'PASS' else 1)
        
    except Exception as e:
        print(f"❌ Error during alignment check: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
