#!/bin/bash

# Quick validation script for .clinerules framework v2.0
# 快速验证脚本 - 包含代码实现验证

set -e

PROJECT_ROOT="${1:-$(pwd)}"

echo "🔍 Quick validation starting for: $PROJECT_ROOT"

# Check if basic structure exists
if [ ! -f "$PROJECT_ROOT/.clinerules/04_phase_gate_checklist.md" ]; then
    echo "❌ .clinerules framework not found"
    exit 1
fi

echo "✅ .clinerules framework detected"

# Check project structure
if [ -f "$PROJECT_ROOT/go.mod" ]; then
    echo "✅ Go project detected"
elif [ -f "$PROJECT_ROOT/package.json" ]; then
    echo "✅ Node.js project detected"
else
    echo "⚠️  Project type not clearly identified"
fi

# Basic file structure check
REQUIRED_DIRS=("internal" "cmd")
for dir in "${REQUIRED_DIRS[@]}"; do
    if [ -d "$PROJECT_ROOT/$dir" ]; then
        echo "✅ $dir directory exists"
    else
        echo "⚠️  $dir directory missing"
    fi
done

# Execute code implementation validation
echo ""
echo "🔍 执行代码实现验证..."
if [ -f "$PROJECT_ROOT/.clinerules/verification_harness/code_implementation_validator.sh" ]; then
    bash "$PROJECT_ROOT/.clinerules/verification_harness/code_implementation_validator.sh" "$PROJECT_ROOT"
    CODE_VALIDATION_RESULT=$?
else
    echo "⚠️  代码实现验证工具未找到"
    CODE_VALIDATION_RESULT=1
fi

# Execute doc-code consistency check
echo ""
echo "🔍 执行文档-代码一致性检查..."
if [ -f "$PROJECT_ROOT/.clinerules/verification_harness/doc_code_consistency_checker.py" ]; then
    python3 "$PROJECT_ROOT/.clinerules/verification_harness/doc_code_consistency_checker.py" "$PROJECT_ROOT"
    DOC_CONSISTENCY_RESULT=$?
else
    echo "⚠️  文档-代码一致性检查工具未找到"
    DOC_CONSISTENCY_RESULT=1
fi

# Overall result
echo ""
echo "📊 验证结果汇总:"
echo "- 代码实现验证: $([ $CODE_VALIDATION_RESULT -eq 0 ] && echo "✅ 通过" || echo "❌ 失败")"
echo "- 文档一致性检查: $([ $DOC_CONSISTENCY_RESULT -eq 0 ] && echo "✅ 通过" || echo "❌ 失败")"

if [ $CODE_VALIDATION_RESULT -eq 0 ] && [ $DOC_CONSISTENCY_RESULT -eq 0 ]; then
    echo "🎯 Quick validation completed - 所有检查通过"
    exit 0
else
    echo "🎯 Quick validation completed - 发现问题需要修复"
    exit 1
fi

# 检查目录
dirs_to_check=(
    "cmd"
    "internal" 
    "pkg"
    "specs"
    "plans"
    ".clinerules"
)

for dir in "${dirs_to_check[@]}"; do
    if [[ -d "$dir" ]]; then
        echo "✅ $dir/ - 存在"
    else
        echo "❌ $dir/ - 缺失"
    fi
done

echo ""
echo "=== 检查.clinerules框架文件 ==="

clinerules_files=(
    ".clinerules/rules/database.md"
    ".clinerules/rules/testing.md"
    ".clinerules/rules/deployment.md"
    ".clinerules/verification_harness/validate_project_structure.sh"
    ".clinerules/project_template/resumix_template_README.md"
)

for file in "${clinerules_files[@]}"; do
    if [[ -f "$file" ]]; then
        echo "✅ $file - 存在"
    else
        echo "❌ $file - 缺失"
    fi
done

echo ""
echo "=== 验证完成 ==="
