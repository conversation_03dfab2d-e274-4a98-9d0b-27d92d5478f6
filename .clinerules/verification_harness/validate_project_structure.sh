#!/bin/bash

# Resumix Project Structure Validation Script
# 验证项目是否符合 .clinerules 框架规范

# set -e  # 暂时禁用严格模式以便调试

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 计数器
TOTAL_CHECKS=0
PASSED_CHECKS=0
FAILED_CHECKS=0

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[PASS]${NC} $1"
    ((PASSED_CHECKS++))
}

log_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[FAIL]${NC} $1"
    ((FAILED_CHECKS++))
}

check_file_exists() {
    local file_path="$1"
    local description="$2"
    ((TOTAL_CHECKS++))
    
    if [[ -f "$file_path" ]]; then
        log_success "$description: $file_path"
        return 0
    else
        log_error "$description: $file_path (文件不存在)"
        return 1
    fi
}

check_directory_exists() {
    local dir_path="$1"
    local description="$2"
    ((TOTAL_CHECKS++))
    
    if [[ -d "$dir_path" ]]; then
        log_success "$description: $dir_path"
        return 0
    else
        log_error "$description: $dir_path (目录不存在)"
        return 1
    fi
}

check_file_content() {
    local file_path="$1"
    local pattern="$2"
    local description="$3"
    ((TOTAL_CHECKS++))
    
    if [[ -f "$file_path" ]] && grep -q "$pattern" "$file_path"; then
        log_success "$description"
        return 0
    else
        log_error "$description (内容验证失败)"
        return 1
    fi
}

# 主验证函数
main() {
    log_info "开始验证 Resumix 项目结构..."
    log_info "验证基于 .clinerules 框架规范"
    echo ""
    
    # 1. 验证根目录必需文件
    log_info "=== 验证根目录必需文件 ==="
    check_file_exists "resumix.intent.yaml" "项目意图文件"
    check_file_exists "deployment.config.toml" "部署配置文件"
    check_file_exists "decision_log.md" "项目决策日志"
    check_file_exists "project_tech_debt_ledger.md" "技术债务记录"
    check_file_exists "README.md" "项目说明文件"
    check_file_exists "go.mod" "Go模块文件"
    check_file_exists "go.sum" "Go依赖校验文件"
    echo ""
    
    # 2. 验证标准目录结构
    log_info "=== 验证标准目录结构 ==="
    check_directory_exists "cmd" "命令行程序目录"
    check_directory_exists "internal" "内部包目录"
    check_directory_exists "pkg" "公共包目录"
    check_directory_exists "configs" "配置文件目录"
    check_directory_exists "scripts" "脚本文件目录"
    check_directory_exists "migrations" "数据库迁移目录"
    check_directory_exists "tests" "测试文件目录"
    echo ""
    
    # 3. 验证规范文档目录
    log_info "=== 验证规范文档目录 ==="
    check_directory_exists "specs" "技术规范目录"
    check_file_exists "specs/resumix.spec.md" "项目技术规范"
    check_directory_exists "plans" "设计计划目录"
    check_directory_exists "plans/architecture" "架构设计目录"
    check_directory_exists "plans/implementation" "实现计划目录"
    echo ""
    
    # 4. 验证.clinerules框架文件
    log_info "=== 验证 .clinerules 框架文件 ==="
    check_directory_exists ".clinerules" ".clinerules框架目录"
    check_directory_exists ".clinerules/rules" "规则文档目录"
    check_file_exists ".clinerules/rules/database.md" "数据库规则文档"
    check_file_exists ".clinerules/rules/testing.md" "测试规则文档"
    check_file_exists ".clinerules/rules/deployment.md" "部署规则文档"
    check_directory_exists ".clinerules/verification_harness" "验证工具目录"
    check_directory_exists ".clinerules/project_template" "项目模板目录"
    echo ""
    
    # 5. 验证文件内容
    log_info "=== 验证关键文件内容 ==="
    check_file_content "resumix.intent.yaml" "project_name.*resumix" "项目意图文件包含项目名称"
    check_file_content "decision_log.md" "Resumix MVP.*项目决策日志" "决策日志文件格式正确"
    check_file_content "project_tech_debt_ledger.md" "技术债务记录" "技术债务文件格式正确"
    echo ""
    
    # 6. 验证Go项目结构
    log_info "=== 验证 Go 项目结构 ==="
    check_directory_exists "cmd/resumix-api" "API服务命令目录"
    check_directory_exists "cmd/resumix-worker" "Worker服务命令目录"
    check_directory_exists "internal/handlers" "HTTP处理器目录"
    check_directory_exists "internal/services" "业务服务目录"
    check_directory_exists "internal/repositories" "数据访问目录"
    check_directory_exists "internal/models" "数据模型目录"
    echo ""
    
    # 7. 验证配置文件
    log_info "=== 验证配置文件 ==="
    check_file_exists "configs/default_profile.yaml" "默认配置文件"
    check_file_exists "docker-compose.prod.yml" "生产环境Docker配置"
    echo ""
    
    # 8. 验证脚本文件
    log_info "=== 验证脚本文件 ==="
    check_file_exists "scripts/deploy-production.sh" "生产部署脚本"
    check_file_exists "scripts/test-all.sh" "测试脚本"
    check_file_exists "Makefile" "构建脚本"
    echo ""
    
    # 9. 检查根目录文档数量
    log_info "=== 检查根目录文档组织 ==="
    ((TOTAL_CHECKS++))
    ROOT_MD_COUNT=$(find . -maxdepth 1 -name "*.md" | wc -l)
    if [[ $ROOT_MD_COUNT -le 10 ]]; then
        log_success "根目录Markdown文档数量合理: $ROOT_MD_COUNT 个"
    else
        log_error "根目录Markdown文档过多: $ROOT_MD_COUNT 个 (建议≤10个)"
    fi
    echo ""
    
    # 10. 验证文档组织建议
    log_info "=== 验证推荐的文档组织 ==="
    if [[ -d "docs" ]]; then
        log_success "存在docs目录用于文档组织"
        check_directory_exists "docs/reports" "报告文档目录"
        check_directory_exists "docs/guides" "指南文档目录"
        check_directory_exists "docs/analysis" "分析文档目录"
    else
        log_warning "建议创建docs目录来组织项目文档"
    fi
    echo ""
    
    # 生成验证报告
    log_info "=== 验证结果汇总 ==="
    echo -e "总检查项: ${BLUE}$TOTAL_CHECKS${NC}"
    echo -e "通过检查: ${GREEN}$PASSED_CHECKS${NC}"
    echo -e "失败检查: ${RED}$FAILED_CHECKS${NC}"
    
    PASS_RATE=$((PASSED_CHECKS * 100 / TOTAL_CHECKS))
    echo -e "通过率: ${BLUE}$PASS_RATE%${NC}"
    
    if [[ $FAILED_CHECKS -eq 0 ]]; then
        echo ""
        log_success "🎉 项目结构完全符合 .clinerules 框架规范!"
        exit 0
    elif [[ $PASS_RATE -ge 80 ]]; then
        echo ""
        log_warning "⚠️  项目结构基本符合规范，但有 $FAILED_CHECKS 项需要改进"
        exit 1
    else
        echo ""
        log_error "❌ 项目结构不符合 .clinerules 框架规范，需要重大调整"
        exit 2
    fi
}

# 检查是否在项目根目录
if [[ ! -f "go.mod" ]] || [[ ! -f "resumix.intent.yaml" ]]; then
    log_error "请在 Resumix 项目根目录运行此脚本"
    exit 1
fi

# 运行主验证
main "$@"
