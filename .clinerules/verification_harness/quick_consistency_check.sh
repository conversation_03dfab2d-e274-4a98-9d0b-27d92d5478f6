#!/bin/bash
# 快速一致性检查脚本
# 基于本次对话中发现的问题，快速检查项目一致性

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目根目录
PROJECT_ROOT="${1:-$(pwd)}"
cd "$PROJECT_ROOT"

echo -e "${BLUE}🔍 开始快速一致性检查...${NC}"
echo "项目根目录: $PROJECT_ROOT"
echo ""

# 检查计数器
TOTAL_CHECKS=0
PASSED_CHECKS=0
FAILED_CHECKS=0

# 检查函数
check_result() {
    local check_name="$1"
    local result="$2"
    local suggestion="$3"
    
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    
    if [ "$result" -eq 0 ]; then
        echo -e "✅ ${GREEN}$check_name${NC}"
        PASSED_CHECKS=$((PASSED_CHECKS + 1))
    else
        echo -e "❌ ${RED}$check_name${NC}"
        if [ -n "$suggestion" ]; then
            echo -e "   💡 ${YELLOW}建议: $suggestion${NC}"
        fi
        FAILED_CHECKS=$((FAILED_CHECKS + 1))
    fi
}

# 1. 检查API端点一致性
echo -e "${BLUE}📡 检查API端点一致性...${NC}"

# 检查前端SSE连接路径
check_sse_endpoints() {
    local frontend_sse_paths=$(find . -name "*.ts" -o -name "*.vue" | xargs grep -l "EventSource\|/events\|/sse" 2>/dev/null || true)
    local backend_sse_routes=$(find . -name "*.go" | xargs grep -l "HandleSSE\|/events" 2>/dev/null || true)
    
    if [ -n "$frontend_sse_paths" ] && [ -n "$backend_sse_routes" ]; then
        # 检查是否有不一致的路径
        local inconsistent=$(grep -r "/api/v1/sse/events" . --include="*.ts" --include="*.vue" 2>/dev/null || true)
        if [ -n "$inconsistent" ]; then
            return 1
        fi
    fi
    return 0
}

check_sse_endpoints
check_result "SSE端点路径一致性" $? "确保前端使用 /v1/events 而不是 /api/v1/sse/events"

# 检查API调用与路由匹配
check_api_routes() {
    # 简单检查：查找明显的路径不匹配
    local mismatched=$(grep -r "localhost:8080/api/v1" . --include="*.ts" --include="*.vue" 2>/dev/null | grep -v "/v1/upload\|/v1/events\|/v1/health" || true)
    if [ -n "$mismatched" ]; then
        return 1
    fi
    return 0
}

check_api_routes
check_result "API路由路径检查" $? "检查前端API调用路径是否与后端路由匹配"

# 2. 检查组件生命周期
echo -e "\n${BLUE}🔄 检查组件生命周期...${NC}"

# 检查Vue组件的onMounted/onUnmounted配对
check_lifecycle_hooks() {
    local vue_files=$(find . -name "*.vue" 2>/dev/null || true)
    local issues=0
    
    for file in $vue_files; do
        if [ -f "$file" ]; then
            local has_mounted=$(grep -c "onMounted(" "$file" 2>/dev/null || echo "0")
            local has_unmounted=$(grep -c "onUnmounted(" "$file" 2>/dev/null || echo "0")
            
            if [ "$has_mounted" -gt 0 ] && [ "$has_unmounted" -eq 0 ]; then
                echo -e "   ⚠️  ${YELLOW}$file: 有onMounted但缺少onUnmounted${NC}"
                issues=$((issues + 1))
            fi
        fi
    done
    
    return $issues
}

check_lifecycle_hooks
check_result "Vue组件生命周期钩子配对" $? "为有onMounted的组件添加onUnmounted钩子"

# 检查事件监听器清理
check_event_listeners() {
    local vue_files=$(find . -name "*.vue" 2>/dev/null || true)
    local issues=0
    
    for file in $vue_files; do
        if [ -f "$file" ]; then
            local add_listeners=$(grep -c "addEventListener" "$file" 2>/dev/null || echo "0")
            local remove_listeners=$(grep -c "removeEventListener" "$file" 2>/dev/null || echo "0")
            
            if [ "$add_listeners" -gt 0 ] && [ "$remove_listeners" -eq 0 ]; then
                echo -e "   ⚠️  ${YELLOW}$file: 添加了事件监听器但未移除${NC}"
                issues=$((issues + 1))
            fi
        fi
    done
    
    return $issues
}

check_event_listeners
check_result "事件监听器清理检查" $? "在onUnmounted中移除所有事件监听器"

# 检查组件事件处理
check_component_events() {
    local vue_files=$(find . -name "*.vue" 2>/dev/null || true)
    local issues=0
    
    for file in $vue_files; do
        if [ -f "$file" ]; then
            # 检查ReportViewer组件是否有@close监听
            if grep -q "ReportViewer" "$file" && ! grep -q "@close=" "$file"; then
                echo -e "   ⚠️  ${YELLOW}$file: ReportViewer组件缺少@close事件监听${NC}"
                issues=$((issues + 1))
            fi
        fi
    done
    
    return $issues
}

check_component_events
check_result "组件事件处理检查" $? "为子组件添加必要的事件监听器"

# 3. 检查文档同步性
echo -e "\n${BLUE}📚 检查文档同步性...${NC}"

# 检查API文档与代码一致性
check_api_docs() {
    local doc_files=$(find . -path "*/docs/*" -name "*.md" -o -path "*/plans/*" -name "*.md" 2>/dev/null || true)
    local issues=0
    
    for doc in $doc_files; do
        if [ -f "$doc" ] && grep -q "API\|api\|端点\|endpoint" "$doc"; then
            # 检查是否包含过时的端点路径
            if grep -q "/api/v1/sse/events" "$doc"; then
                echo -e "   ⚠️  ${YELLOW}$doc: 包含过时的SSE端点路径${NC}"
                issues=$((issues + 1))
            fi
        fi
    done
    
    return $issues
}

check_api_docs
check_result "API文档一致性检查" $? "更新文档中的API端点路径"

# 检查README文件是否存在
check_readme() {
    if [ ! -f "README.md" ] && [ ! -f "readme.md" ]; then
        return 1
    fi
    return 0
}

check_readme
check_result "README文件存在性" $? "创建项目README文件"

# 4. 检查配置一致性
echo -e "\n${BLUE}⚙️ 检查配置一致性...${NC}"

# 检查环境变量文件
check_env_files() {
    local issues=0
    
    # 检查.env.example是否存在
    if [ ! -f ".env.example" ]; then
        echo -e "   ⚠️  ${YELLOW}缺少 .env.example 文件${NC}"
        issues=$((issues + 1))
    fi
    
    # 检查前端项目的环境变量
    for frontend_dir in "resume_app" "resumix-web"; do
        if [ -d "$frontend_dir" ]; then
            if [ ! -f "$frontend_dir/.env.example" ]; then
                echo -e "   ⚠️  ${YELLOW}$frontend_dir 缺少 .env.example 文件${NC}"
                issues=$((issues + 1))
            fi
        fi
    done
    
    return $issues
}

check_env_files
check_result "环境变量文件检查" $? "创建缺失的.env.example文件"

# 检查package.json版本一致性
check_package_versions() {
    local package_files=$(find . -name "package.json" 2>/dev/null || true)
    local versions=$(echo "$package_files" | xargs grep -h '"version"' 2>/dev/null | sort | uniq | wc -l)
    
    if [ "$versions" -gt 1 ]; then
        echo -e "   ⚠️  ${YELLOW}发现不同的package.json版本号${NC}"
        return 1
    fi
    return 0
}

if command -v node >/dev/null 2>&1; then
    check_package_versions
    check_result "Package.json版本一致性" $? "统一所有package.json的版本号"
fi

# 5. 检查构建配置
echo -e "\n${BLUE}🔨 检查构建配置...${NC}"

# 检查TypeScript配置
check_typescript_config() {
    local ts_projects=$(find . -name "tsconfig.json" 2>/dev/null || true)
    local issues=0
    
    for config in $ts_projects; do
        if [ -f "$config" ]; then
            # 检查是否启用了严格模式
            if ! grep -q '"strict": true' "$config"; then
                echo -e "   ⚠️  ${YELLOW}$config: 建议启用TypeScript严格模式${NC}"
                issues=$((issues + 1))
            fi
        fi
    done
    
    return $issues
}

if command -v tsc >/dev/null 2>&1; then
    check_typescript_config
    check_result "TypeScript配置检查" $? "在tsconfig.json中启用strict模式"
fi

# 检查Go模块
check_go_modules() {
    if [ -f "go.mod" ]; then
        # 检查是否有未使用的依赖
        if command -v go >/dev/null 2>&1; then
            go mod tidy >/dev/null 2>&1
            if [ $? -ne 0 ]; then
                return 1
            fi
        fi
    fi
    return 0
}

if [ -f "go.mod" ]; then
    check_go_modules
    check_result "Go模块检查" $? "运行 go mod tidy 清理依赖"
fi

# 6. 生成总结报告
echo -e "\n${BLUE}📊 检查总结${NC}"
echo "=================================="
echo -e "总检查项: ${BLUE}$TOTAL_CHECKS${NC}"
echo -e "通过: ${GREEN}$PASSED_CHECKS${NC}"
echo -e "失败: ${RED}$FAILED_CHECKS${NC}"

if [ $FAILED_CHECKS -eq 0 ]; then
    echo -e "\n🎉 ${GREEN}所有检查都通过了！项目一致性良好。${NC}"
    exit 0
else
    echo -e "\n⚠️  ${YELLOW}发现 $FAILED_CHECKS 个问题需要修复。${NC}"
    echo ""
    echo "建议的修复优先级："
    echo "1. 🔴 API端点不一致 - 可能导致功能失效"
    echo "2. 🟡 组件生命周期问题 - 可能导致资源泄漏"
    echo "3. 🟢 文档和配置问题 - 影响开发体验"
    echo ""
    echo "运行详细检查："
    echo "python3 .clinerules/verification_harness/consistency_validator.py"
    
    if [ $FAILED_CHECKS -gt 5 ]; then
        exit 2  # 严重问题
    else
        exit 1  # 一般问题
    fi
fi
