#!/usr/bin/env python3
"""
一致性验证器
基于本次对话中发现的问题，自动检查项目的一致性
"""

import os
import re
import json
import yaml
import sys
from pathlib import Path
from typing import Dict, List, Tuple, Any
from dataclasses import dataclass
from enum import Enum

class ViolationSeverity(Enum):
    CRITICAL = "critical"
    MAJOR = "major"
    MINOR = "minor"

@dataclass
class ConsistencyViolation:
    type: str
    severity: ViolationSeverity
    message: str
    file_path: str
    line_number: int = 0
    suggestion: str = ""

class ConsistencyValidator:
    def __init__(self, project_root: str):
        self.project_root = Path(project_root)
        self.violations: List[ConsistencyViolation] = []
        
    def validate_all(self) -> List[ConsistencyViolation]:
        """运行所有一致性检查"""
        print("🔍 开始一致性验证...")
        
        self.check_api_consistency()
        self.check_component_lifecycle()
        self.check_documentation_sync()
        self.check_configuration_consistency()
        
        return self.violations
    
    def check_api_consistency(self):
        """检查API端点一致性"""
        print("📡 检查API端点一致性...")
        
        # 提取后端路由
        backend_routes = self._extract_backend_routes()
        
        # 提取前端API调用
        frontend_calls = self._extract_frontend_api_calls()
        
        # 检查一致性
        for frontend_call in frontend_calls:
            if not self._is_route_defined(frontend_call, backend_routes):
                self.violations.append(ConsistencyViolation(
                    type="api_endpoint_mismatch",
                    severity=ViolationSeverity.CRITICAL,
                    message=f"前端调用的API端点未在后端定义: {frontend_call['endpoint']}",
                    file_path=frontend_call['file'],
                    line_number=frontend_call['line'],
                    suggestion="检查后端路由定义或修正前端API调用路径"
                ))
    
    def check_component_lifecycle(self):
        """检查组件生命周期完整性"""
        print("🔄 检查组件生命周期...")
        
        vue_files = list(self.project_root.glob("**/src/**/*.vue"))
        
        for vue_file in vue_files:
            content = vue_file.read_text(encoding='utf-8')
            
            # 检查onMounted和onUnmounted配对
            has_mounted = 'onMounted(' in content
            has_unmounted = 'onUnmounted(' in content
            
            if has_mounted and not has_unmounted:
                self.violations.append(ConsistencyViolation(
                    type="missing_unmounted_hook",
                    severity=ViolationSeverity.MAJOR,
                    message="组件有onMounted但缺少onUnmounted，可能导致资源泄漏",
                    file_path=str(vue_file),
                    suggestion="添加onUnmounted钩子清理资源"
                ))
            
            # 检查事件监听器清理
            event_listeners = re.findall(r'addEventListener\([\'"]([^\'"]+)[\'"]', content)
            remove_listeners = re.findall(r'removeEventListener\([\'"]([^\'"]+)[\'"]', content)
            
            for listener in event_listeners:
                if listener not in remove_listeners:
                    self.violations.append(ConsistencyViolation(
                        type="unremoved_event_listener",
                        severity=ViolationSeverity.MAJOR,
                        message=f"事件监听器 '{listener}' 未在组件卸载时移除",
                        file_path=str(vue_file),
                        suggestion=f"在onUnmounted中添加removeEventListener('{listener}')"
                    ))
            
            # 检查子组件事件处理
            self._check_component_event_handling(vue_file, content)
    
    def check_documentation_sync(self):
        """检查文档同步性"""
        print("📚 检查文档同步性...")
        
        # 检查API文档与实际端点的一致性
        api_docs = list(self.project_root.glob("**/docs/**/*api*.md"))
        api_docs.extend(list(self.project_root.glob("**/plans/**/*api*.md")))
        
        backend_routes = self._extract_backend_routes()
        
        for doc_file in api_docs:
            content = doc_file.read_text(encoding='utf-8')
            
            # 提取文档中的API端点
            doc_endpoints = re.findall(r'(?:GET|POST|PUT|DELETE|PATCH)\s+([/\w\-:{}]+)', content)
            
            for endpoint in doc_endpoints:
                # 清理端点格式
                clean_endpoint = re.sub(r'\{[^}]+\}', ':id', endpoint)
                
                if not any(clean_endpoint in route['path'] for route in backend_routes):
                    self.violations.append(ConsistencyViolation(
                        type="doc_endpoint_mismatch",
                        severity=ViolationSeverity.MINOR,
                        message=f"文档中的端点在代码中未找到: {endpoint}",
                        file_path=str(doc_file),
                        suggestion="更新文档或检查端点实现"
                    ))
    
    def check_configuration_consistency(self):
        """检查配置一致性"""
        print("⚙️ 检查配置一致性...")
        
        # 检查环境变量一致性
        env_example = self.project_root / ".env.example"
        env_files = [
            self.project_root / ".env",
            self.project_root / "resume_app" / ".env.example",
            self.project_root / "resumix-web" / ".env.example"
        ]
        
        if env_example.exists():
            example_vars = self._extract_env_vars(env_example)
            
            for env_file in env_files:
                if env_file.exists():
                    file_vars = self._extract_env_vars(env_file)
                    
                    # 检查缺失的变量
                    missing_vars = set(example_vars.keys()) - set(file_vars.keys())
                    for var in missing_vars:
                        self.violations.append(ConsistencyViolation(
                            type="missing_env_var",
                            severity=ViolationSeverity.MINOR,
                            message=f"环境变量 {var} 在 {env_file.name} 中缺失",
                            file_path=str(env_file),
                            suggestion=f"从 .env.example 复制 {var} 的定义"
                        ))
    
    def _extract_backend_routes(self) -> List[Dict[str, Any]]:
        """提取后端路由定义"""
        routes = []
        
        # 查找Go文件中的路由定义
        go_files = list(self.project_root.glob("**/cmd/**/*.go"))
        go_files.extend(list(self.project_root.glob("**/internal/**/*.go")))
        
        for go_file in go_files:
            try:
                content = go_file.read_text(encoding='utf-8')
                
                # 匹配路由定义模式
                route_patterns = [
                    r'router\.(\w+)\([\'"]([^\'"]+)[\'"]',
                    r'\.(\w+)\([\'"]([^\'"]+)[\'"].*handler',
                    r'Handle\w*\([\'"](\w+)[\'"],\s*[\'"]([^\'"]+)[\'"]'
                ]
                
                for pattern in route_patterns:
                    matches = re.findall(pattern, content)
                    for match in matches:
                        if len(match) == 2:
                            method, path = match
                            routes.append({
                                'method': method.upper(),
                                'path': path,
                                'file': str(go_file)
                            })
            except Exception as e:
                print(f"警告: 无法读取文件 {go_file}: {e}")
        
        return routes
    
    def _extract_frontend_api_calls(self) -> List[Dict[str, Any]]:
        """提取前端API调用"""
        api_calls = []
        
        # 查找TypeScript和Vue文件
        ts_files = list(self.project_root.glob("**/src/**/*.ts"))
        vue_files = list(self.project_root.glob("**/src/**/*.vue"))
        
        all_files = ts_files + vue_files
        
        for file_path in all_files:
            try:
                content = file_path.read_text(encoding='utf-8')
                lines = content.split('\n')
                
                # 匹配API调用模式
                api_patterns = [
                    r'fetch\([\'"`]([^\'"` ]+)[\'"`]',
                    r'axios\.\w+\([\'"`]([^\'"` ]+)[\'"`]',
                    r'new EventSource\([\'"`]([^\'"` ]+)[\'"`]',
                    r'API_BASE_URL\s*\+\s*[\'"`]([^\'"` ]+)[\'"`]'
                ]
                
                for line_num, line in enumerate(lines, 1):
                    for pattern in api_patterns:
                        matches = re.findall(pattern, line)
                        for match in matches:
                            api_calls.append({
                                'endpoint': match,
                                'file': str(file_path),
                                'line': line_num
                            })
            except Exception as e:
                print(f"警告: 无法读取文件 {file_path}: {e}")
        
        return api_calls
    
    def _is_route_defined(self, frontend_call: Dict[str, Any], backend_routes: List[Dict[str, Any]]) -> bool:
        """检查前端调用的路由是否在后端定义"""
        endpoint = frontend_call['endpoint']
        
        # 清理端点路径
        if endpoint.startswith('${'):
            return True  # 跳过模板字符串
        
        for route in backend_routes:
            if endpoint in route['path'] or route['path'] in endpoint:
                return True
        
        return False
    
    def _check_component_event_handling(self, vue_file: Path, content: str):
        """检查组件事件处理"""
        # 查找子组件使用
        component_uses = re.findall(r'<(\w+Component|\w+Viewer)[^>]*>', content)
        
        for component in component_uses:
            # 检查是否有@close等事件监听
            if 'Viewer' in component and '@close=' not in content:
                self.violations.append(ConsistencyViolation(
                    type="missing_event_handler",
                    severity=ViolationSeverity.MAJOR,
                    message=f"组件 {component} 可能需要 @close 事件处理器",
                    file_path=str(vue_file),
                    suggestion=f"为 {component} 添加 @close 事件监听器"
                ))
    
    def _extract_env_vars(self, env_file: Path) -> Dict[str, str]:
        """提取环境变量"""
        vars_dict = {}
        
        try:
            content = env_file.read_text(encoding='utf-8')
            for line in content.split('\n'):
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    vars_dict[key.strip()] = value.strip()
        except Exception as e:
            print(f"警告: 无法读取环境变量文件 {env_file}: {e}")
        
        return vars_dict
    
    def generate_report(self) -> str:
        """生成验证报告"""
        if not self.violations:
            return "✅ 所有一致性检查通过！"
        
        report = ["🚨 发现一致性问题:", ""]
        
        # 按严重程度分组
        by_severity = {}
        for violation in self.violations:
            severity = violation.severity.value
            if severity not in by_severity:
                by_severity[severity] = []
            by_severity[severity].append(violation)
        
        # 生成报告
        severity_order = [ViolationSeverity.CRITICAL, ViolationSeverity.MAJOR, ViolationSeverity.MINOR]
        
        for severity in severity_order:
            severity_key = severity.value
            if severity_key in by_severity:
                violations = by_severity[severity_key]
                report.append(f"## {severity_key.upper()} ({len(violations)} 个问题)")
                report.append("")
                
                for i, violation in enumerate(violations, 1):
                    report.append(f"{i}. **{violation.type}**")
                    report.append(f"   文件: {violation.file_path}")
                    if violation.line_number:
                        report.append(f"   行号: {violation.line_number}")
                    report.append(f"   问题: {violation.message}")
                    if violation.suggestion:
                        report.append(f"   建议: {violation.suggestion}")
                    report.append("")
        
        # 添加统计信息
        total = len(self.violations)
        critical = len(by_severity.get('critical', []))
        major = len(by_severity.get('major', []))
        minor = len(by_severity.get('minor', []))
        
        report.extend([
            "## 统计信息",
            f"- 总问题数: {total}",
            f"- 严重问题: {critical}",
            f"- 重要问题: {major}",
            f"- 轻微问题: {minor}",
            "",
            "## 建议",
            "1. 优先修复严重问题，这些可能导致功能失效",
            "2. 在当前迭代内修复重要问题",
            "3. 计划在下个迭代修复轻微问题",
            "4. 考虑添加自动化检查防止类似问题"
        ])
        
        return "\n".join(report)

def main():
    if len(sys.argv) > 1:
        project_root = sys.argv[1]
    else:
        project_root = os.getcwd()
    
    validator = ConsistencyValidator(project_root)
    violations = validator.validate_all()
    
    report = validator.generate_report()
    print("\n" + "="*60)
    print(report)
    print("="*60)
    
    # 返回适当的退出码
    if any(v.severity == ViolationSeverity.CRITICAL for v in violations):
        sys.exit(2)  # 严重问题
    elif any(v.severity == ViolationSeverity.MAJOR for v in violations):
        sys.exit(1)  # 重要问题
    else:
        sys.exit(0)  # 无问题或仅轻微问题

if __name__ == "__main__":
    main()
