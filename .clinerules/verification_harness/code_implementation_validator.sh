#!/bin/bash

# Code Implementation Validator v1.0
# 代码实现验证工具 - 检查代码实现与文档的一致性

set -e

PROJECT_ROOT="${1:-$(pwd)}"
REPORT_DIR="${PROJECT_ROOT}/reports/verification"
REPORT_FILE="${REPORT_DIR}/code_validation_report.md"

# 创建报告目录
mkdir -p "$REPORT_DIR"

echo "🔍 代码实现验证工具启动..."
echo "📁 项目根目录: $PROJECT_ROOT"
echo "📄 报告文件: $REPORT_FILE"

# 创建报告文件
cat > "$REPORT_FILE" << 'EOF'
# 代码实现验证报告

**生成时间**: $(date)
**项目路径**: $PROJECT_ROOT

## 验证摘要

| 检查项 | 状态 | 详情 |
|--------|------|------|
EOF

# 1. TODO/FIXME/HACK 扫描
echo "🔍 1. 扫描 TODO/FIXME/HACK 注释..."
TODO_COUNT=0
CRITICAL_TODOS=()

# 扫描Go文件
if [ -d "$PROJECT_ROOT" ]; then
    while IFS= read -r -d '' file; do
        if grep -n -E "(TODO|FIXME|HACK)" "$file" > /dev/null 2>&1; then
            todos=$(grep -n -E "(TODO|FIXME|HACK)" "$file")
            while IFS= read -r line; do
                TODO_COUNT=$((TODO_COUNT + 1))
                # 检查是否是关键功能的TODO
                if echo "$line" | grep -E "(保存|save|create|update|delete|persist)" > /dev/null; then
                    CRITICAL_TODOS+=("$file:$line")
                fi
            done <<< "$todos"
        fi
    done < <(find "$PROJECT_ROOT" -name "*.go" -type f -print0)
fi

echo "| TODO扫描 | $([ $TODO_COUNT -lt 10 ] && echo "✅ 通过" || echo "❌ 失败") | 发现 $TODO_COUNT 个TODO注释 |" >> "$REPORT_FILE"

# 2. 空方法体检查
echo "🔍 2. 检查空方法体..."
EMPTY_METHODS=0

if [ -d "$PROJECT_ROOT" ]; then
    while IFS= read -r -d '' file; do
        # 查找空方法体或只有panic的方法
        empty_methods=$(grep -n -A 3 "func.*{" "$file" | grep -E "(^\s*}$|panic\(|return nil)" | wc -l)
        EMPTY_METHODS=$((EMPTY_METHODS + empty_methods))
    done < <(find "$PROJECT_ROOT" -name "*.go" -type f -print0)
fi

echo "| 空方法检查 | $([ $EMPTY_METHODS -lt 5 ] && echo "✅ 通过" || echo "❌ 失败") | 发现 $EMPTY_METHODS 个可能的空方法 |" >> "$REPORT_FILE"

# 3. API端点实现检查
echo "🔍 3. 检查API端点实现..."
API_ENDPOINTS=0
UNIMPLEMENTED_APIS=0

if [ -f "$PROJECT_ROOT/internal/handlers"/*.go ]; then
    # 查找API处理函数
    API_ENDPOINTS=$(grep -r "func.*Handler" "$PROJECT_ROOT/internal/handlers" | wc -l)
    # 查找未实现的API（返回not implemented等）
    UNIMPLEMENTED_APIS=$(grep -r -E "(not implemented|coming soon|TODO)" "$PROJECT_ROOT/internal/handlers" | wc -l)
fi

echo "| API实现检查 | $([ $UNIMPLEMENTED_APIS -eq 0 ] && echo "✅ 通过" || echo "❌ 失败") | $API_ENDPOINTS 个端点，$UNIMPLEMENTED_APIS 个未实现 |" >> "$REPORT_FILE"

# 4. 数据库操作检查
echo "🔍 4. 检查数据库操作实现..."
DB_OPERATIONS=0
MISSING_DB_OPS=0

if [ -d "$PROJECT_ROOT/internal/repositories" ]; then
    # 查找数据库操作方法
    DB_OPERATIONS=$(grep -r -E "(Create|Update|Delete|Get)" "$PROJECT_ROOT/internal/repositories" | wc -l)
    # 查找未实现的数据库操作
    MISSING_DB_OPS=$(grep -r -E "(TODO|panic|return nil)" "$PROJECT_ROOT/internal/repositories" | wc -l)
fi

echo "| 数据库操作检查 | $([ $MISSING_DB_OPS -lt 3 ] && echo "✅ 通过" || echo "❌ 失败") | $DB_OPERATIONS 个操作，$MISSING_DB_OPS 个可能未实现 |" >> "$REPORT_FILE"

# 5. 关键TODO详情
echo "" >> "$REPORT_FILE"
echo "## 关键TODO详情" >> "$REPORT_FILE"
echo "" >> "$REPORT_FILE"

if [ ${#CRITICAL_TODOS[@]} -gt 0 ]; then
    echo "⚠️ **发现关键功能TODO:**" >> "$REPORT_FILE"
    echo "" >> "$REPORT_FILE"
    for todo in "${CRITICAL_TODOS[@]}"; do
        echo "- $todo" >> "$REPORT_FILE"
    done
else
    echo "✅ **未发现关键功能TODO**" >> "$REPORT_FILE"
fi

# 6. 建议修复措施
echo "" >> "$REPORT_FILE"
echo "## 建议修复措施" >> "$REPORT_FILE"
echo "" >> "$REPORT_FILE"

if [ $TODO_COUNT -gt 10 ]; then
    echo "1. **TODO清理**: 项目中有 $TODO_COUNT 个TODO注释，建议优先处理关键功能相关的TODO" >> "$REPORT_FILE"
fi

if [ $UNIMPLEMENTED_APIS -gt 0 ]; then
    echo "2. **API实现**: 发现 $UNIMPLEMENTED_APIS 个未实现的API端点，需要补充实际业务逻辑" >> "$REPORT_FILE"
fi

if [ $MISSING_DB_OPS -gt 2 ]; then
    echo "3. **数据库操作**: 发现 $MISSING_DB_OPS 个可能未实现的数据库操作，需要验证实际功能" >> "$REPORT_FILE"
fi

# 7. 验证结论
echo "" >> "$REPORT_FILE"
echo "## 验证结论" >> "$REPORT_FILE"
echo "" >> "$REPORT_FILE"

TOTAL_ISSUES=$((TODO_COUNT + EMPTY_METHODS + UNIMPLEMENTED_APIS + MISSING_DB_OPS))

if [ $TOTAL_ISSUES -lt 10 ] && [ ${#CRITICAL_TODOS[@]} -eq 0 ]; then
    echo "✅ **代码实现验证通过** - 代码实现基本完整，可以进入下一阶段" >> "$REPORT_FILE"
    VALIDATION_RESULT="PASS"
elif [ ${#CRITICAL_TODOS[@]} -gt 0 ] || [ $UNIMPLEMENTED_APIS -gt 5 ]; then
    echo "❌ **代码实现验证失败** - 存在关键功能未实现，需要修复后重新验证" >> "$REPORT_FILE"
    VALIDATION_RESULT="FAIL"
else
    echo "⚠️ **代码实现验证部分通过** - 存在一些问题但不影响核心功能，建议修复" >> "$REPORT_FILE"
    VALIDATION_RESULT="PARTIAL"
fi

echo ""
echo "📊 验证完成！"
echo "📄 详细报告: $REPORT_FILE"
echo "🎯 验证结果: $VALIDATION_RESULT"

# 返回适当的退出码
case $VALIDATION_RESULT in
    "PASS") exit 0 ;;
    "PARTIAL") exit 1 ;;
    "FAIL") exit 2 ;;
esac
EOF

chmod +x "$PROJECT_ROOT/.clinerules/verification_harness/code_implementation_validator.sh"
