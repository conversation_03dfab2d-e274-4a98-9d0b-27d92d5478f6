#!/usr/bin/env python3
"""
文档-代码一致性检查工具 v1.0
检查Phase文档定义与实际代码实现的一致性
"""

import os
import re
import json
import sys
from pathlib import Path
from typing import Dict, List, Set, Tuple

class DocCodeConsistencyChecker:
    def __init__(self, project_root: str):
        self.project_root = Path(project_root)
        self.issues = []
        self.stats = {
            'documented_apis': 0,
            'implemented_apis': 0,
            'missing_implementations': 0,
            'undocumented_implementations': 0,
            'todo_in_critical_paths': 0
        }
    
    def extract_documented_apis(self) -> Set[str]:
        """从Phase文档中提取已定义的API端点"""
        documented_apis = set()
        
        # 扫描plans目录下的文档
        plans_dir = self.project_root / "plans"
        if plans_dir.exists():
            for doc_file in plans_dir.rglob("*.md"):
                content = doc_file.read_text(encoding='utf-8', errors='ignore')
                
                # 提取API端点定义
                api_patterns = [
                    r'(GET|POST|PUT|DELETE|PATCH)\s+(/[^\s\n]+)',
                    r'`(GET|POST|PUT|DELETE|PATCH)\s+([^`]+)`',
                    r'endpoint[:\s]+["`]?([A-Z]+)\s+([^"`\s\n]+)'
                ]
                
                for pattern in api_patterns:
                    matches = re.findall(pattern, content, re.IGNORECASE)
                    for match in matches:
                        if len(match) == 2:
                            method, path = match
                            documented_apis.add(f"{method.upper()} {path}")
        
        self.stats['documented_apis'] = len(documented_apis)
        return documented_apis
    
    def extract_implemented_apis(self) -> Set[str]:
        """从代码中提取已实现的API端点"""
        implemented_apis = set()
        
        # 扫描handlers目录
        handlers_dir = self.project_root / "internal" / "handlers"
        if handlers_dir.exists():
            for go_file in handlers_dir.rglob("*.go"):
                content = go_file.read_text(encoding='utf-8', errors='ignore')
                
                # 提取Gin路由定义
                route_patterns = [
                    r'\.([A-Z]+)\s*\(\s*["\']([^"\']+)["\']',
                    r'router\.([A-Z]+)\s*\(\s*["\']([^"\']+)["\']',
                    r'group\.([A-Z]+)\s*\(\s*["\']([^"\']+)["\']'
                ]
                
                for pattern in route_patterns:
                    matches = re.findall(pattern, content, re.IGNORECASE)
                    for method, path in matches:
                        implemented_apis.add(f"{method.upper()} {path}")
        
        self.stats['implemented_apis'] = len(implemented_apis)
        return implemented_apis
    
    def find_critical_todos(self) -> List[Tuple[str, str, str]]:
        """查找关键路径中的TODO注释"""
        critical_todos = []
        
        # 关键文件模式
        critical_patterns = [
            "*/services/*.go",
            "*/repositories/*.go", 
            "*/handlers/*.go"
        ]
        
        for pattern in critical_patterns:
            for go_file in self.project_root.rglob(pattern):
                content = go_file.read_text(encoding='utf-8', errors='ignore')
                lines = content.split('\n')
                
                for i, line in enumerate(lines, 1):
                    if re.search(r'(TODO|FIXME|HACK).*(?:保存|save|create|update|delete|persist)', line, re.IGNORECASE):
                        critical_todos.append((str(go_file), str(i), line.strip()))
        
        self.stats['todo_in_critical_paths'] = len(critical_todos)
        return critical_todos
    
    def check_method_implementations(self) -> List[Dict]:
        """检查方法实现完整性"""
        incomplete_methods = []
        
        service_dir = self.project_root / "internal" / "services"
        if service_dir.exists():
            for go_file in service_dir.rglob("*.go"):
                content = go_file.read_text(encoding='utf-8', errors='ignore')
                
                # 查找方法定义
                method_pattern = r'func\s+\([^)]+\)\s+(\w+)\s*\([^)]*\)\s*[^{]*\{'
                methods = re.findall(method_pattern, content)
                
                for method in methods:
                    # 检查方法体是否只有TODO或panic
                    method_body_pattern = rf'func\s+\([^)]+\)\s+{method}\s*\([^)]*\)\s*[^{{]*\{{([^}}]*)}}'
                    body_match = re.search(method_body_pattern, content, re.DOTALL)
                    
                    if body_match:
                        body = body_match.group(1).strip()
                        if (not body or 
                            'TODO' in body or 
                            'panic(' in body or 
                            body == 'return nil' or
                            'not implemented' in body.lower()):
                            incomplete_methods.append({
                                'file': str(go_file),
                                'method': method,
                                'issue': 'incomplete_implementation'
                            })
        
        return incomplete_methods
    
    def generate_report(self) -> str:
        """生成一致性检查报告"""
        documented_apis = self.extract_documented_apis()
        implemented_apis = self.extract_implemented_apis()
        critical_todos = self.find_critical_todos()
        incomplete_methods = self.check_method_implementations()
        
        # 计算差异
        missing_implementations = documented_apis - implemented_apis
        undocumented_implementations = implemented_apis - documented_apis
        
        self.stats['missing_implementations'] = len(missing_implementations)
        self.stats['undocumented_implementations'] = len(undocumented_implementations)
        
        # 生成报告
        report = f"""# 文档-代码一致性检查报告

**检查时间**: {os.popen('date').read().strip()}
**项目路径**: {self.project_root}

## 检查统计

| 指标 | 数量 | 状态 |
|------|------|------|
| 文档定义的API | {self.stats['documented_apis']} | - |
| 代码实现的API | {self.stats['implemented_apis']} | - |
| 缺失的实现 | {self.stats['missing_implementations']} | {'✅' if self.stats['missing_implementations'] == 0 else '❌'} |
| 未文档化的实现 | {self.stats['undocumented_implementations']} | {'✅' if self.stats['undocumented_implementations'] <= 2 else '⚠️'} |
| 关键路径TODO | {self.stats['todo_in_critical_paths']} | {'✅' if self.stats['todo_in_critical_paths'] == 0 else '❌'} |
| 不完整方法 | {len(incomplete_methods)} | {'✅' if len(incomplete_methods) <= 3 else '❌'} |

## 详细问题

### 1. 缺失的API实现
"""
        
        if missing_implementations:
            report += "\n⚠️ **以下API已在文档中定义但未实现:**\n\n"
            for api in sorted(missing_implementations):
                report += f"- `{api}`\n"
        else:
            report += "\n✅ **所有文档定义的API都已实现**\n"
        
        report += "\n### 2. 关键路径TODO\n"
        if critical_todos:
            report += "\n❌ **关键功能中发现TODO注释:**\n\n"
            for file_path, line_num, line_content in critical_todos:
                report += f"- `{file_path}:{line_num}` - {line_content}\n"
        else:
            report += "\n✅ **关键路径无TODO阻塞**\n"
        
        report += "\n### 3. 不完整的方法实现\n"
        if incomplete_methods:
            report += "\n⚠️ **发现不完整的方法实现:**\n\n"
            for method_info in incomplete_methods:
                report += f"- `{method_info['method']}` in `{method_info['file']}` - {method_info['issue']}\n"
        else:
            report += "\n✅ **所有方法都有完整实现**\n"
        
        # 总体评估
        total_critical_issues = (self.stats['missing_implementations'] + 
                               self.stats['todo_in_critical_paths'] + 
                               len([m for m in incomplete_methods if 'create' in m['method'].lower() or 'save' in m['method'].lower()]))
        
        report += f"\n## 总体评估\n\n"
        if total_critical_issues == 0:
            report += "✅ **一致性检查通过** - 文档与代码实现基本一致\n"
            status = "PASS"
        elif total_critical_issues <= 3:
            report += "⚠️ **一致性检查部分通过** - 存在少量不一致，建议修复\n"
            status = "PARTIAL"
        else:
            report += "❌ **一致性检查失败** - 存在严重的文档-代码不一致问题\n"
            status = "FAIL"
        
        report += f"\n**建议措施:**\n"
        if missing_implementations:
            report += "1. 实现缺失的API端点\n"
        if critical_todos:
            report += "2. 完成关键路径中的TODO项目\n"
        if incomplete_methods:
            report += "3. 补充不完整的方法实现\n"
        
        return report, status

def main():
    if len(sys.argv) != 2:
        print("Usage: python3 doc_code_consistency_checker.py <project_root>")
        sys.exit(1)
    
    project_root = sys.argv[1]
    checker = DocCodeConsistencyChecker(project_root)
    
    print("🔍 开始文档-代码一致性检查...")
    report, status = checker.generate_report()
    
    # 保存报告
    report_file = Path(project_root) / "reports" / "verification" / "doc_code_consistency_report.md"
    report_file.parent.mkdir(parents=True, exist_ok=True)
    report_file.write_text(report, encoding='utf-8')
    
    print(f"📄 报告已生成: {report_file}")
    print(f"🎯 检查结果: {status}")
    
    # 返回适当的退出码
    exit_codes = {"PASS": 0, "PARTIAL": 1, "FAIL": 2}
    sys.exit(exit_codes.get(status, 2))

if __name__ == "__main__":
    main()
