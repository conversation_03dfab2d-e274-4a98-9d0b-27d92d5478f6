package main

import (
	"encoding/json"
	"fmt"
	"log"
	"os"
	"strings"
)

// CostEstimate 成本估算结果
type CostEstimate struct {
	TokenConsumption    int    `json:"token_consumption"`
	ToolCalls          int    `json:"tool_calls"`
	HumanReviewPoints  int    `json:"human_review_points"`
	EstimatedTime      string `json:"estimated_time"`
	RiskLevel          string `json:"risk_level"`
	BusinessValue      string `json:"business_value"`
	Reasoning          string `json:"reasoning"`
}

// TaskContext 任务上下文
type TaskContext struct {
	Complexity     string `json:"complexity"`
	Domain         string `json:"domain"`
	FileCount      int    `json:"file_count"`
	Environment    string `json:"environment"`
	BusinessValue  string `json:"business_value"`
}

// CostEstimator 成本估算器
type CostEstimator struct {
	baseCosts map[string]CostEstimate
}

// NewCostEstimator 创建新的成本估算器
func NewCostEstimator() *CostEstimator {
	baseCosts := map[string]CostEstimate{
		"trivial": {
			TokenConsumption:   500,
			ToolCalls:         2,
			HumanReviewPoints: 0,
			EstimatedTime:     "5-10分钟",
			RiskLevel:         "低",
			Reasoning:         "微小任务，风险极低，可快速完成",
		},
		"simple": {
			TokenConsumption:   2000,
			ToolCalls:         8,
			HumanReviewPoints: 1,
			EstimatedTime:     "30-60分钟",
			RiskLevel:         "低",
			Reasoning:         "简单任务，影响范围可控，需要基本验证",
		},
		"complex": {
			TokenConsumption:   8000,
			ToolCalls:         25,
			HumanReviewPoints: 3,
			EstimatedTime:     "2-8小时",
			RiskLevel:         "中",
			Reasoning:         "复杂任务，需要完整的设计和验证流程",
		},
	}

	return &CostEstimator{baseCosts: baseCosts}
}

// EstimateCost 估算任务成本
func (ce *CostEstimator) EstimateCost(taskDescription string, context TaskContext) CostEstimate {
	// 获取基础成本
	baseCost, exists := ce.baseCosts[context.Complexity]
	if !exists {
		baseCost = ce.baseCosts["simple"] // 默认为简单任务
	}

	// 复制基础成本作为起点
	estimate := baseCost

	// 根据上下文调整成本
	estimate = ce.adjustForContext(estimate, context)
	estimate = ce.adjustForDescription(estimate, taskDescription)

	// 设置业务价值
	estimate.BusinessValue = context.BusinessValue
	if estimate.BusinessValue == "" {
		estimate.BusinessValue = "待确认"
	}

	return estimate
}

// adjustForContext 根据上下文调整成本
func (ce *CostEstimator) adjustForContext(estimate CostEstimate, context TaskContext) CostEstimate {
	// 文件数量影响
	if context.FileCount > 5 {
		estimate.TokenConsumption = int(float64(estimate.TokenConsumption) * 1.5)
		estimate.ToolCalls = int(float64(estimate.ToolCalls) * 1.3)
		estimate.HumanReviewPoints++
	}

	// 环境影响
	if context.Environment == "production" {
		estimate.TokenConsumption = int(float64(estimate.TokenConsumption) * 1.2)
		estimate.HumanReviewPoints++
		estimate.RiskLevel = ce.escalateRisk(estimate.RiskLevel)
	}

	// 领域影响
	switch context.Domain {
	case "architecture", "security":
		estimate.TokenConsumption = int(float64(estimate.TokenConsumption) * 1.4)
		estimate.HumanReviewPoints++
		estimate.RiskLevel = ce.escalateRisk(estimate.RiskLevel)
	case "database":
		estimate.TokenConsumption = int(float64(estimate.TokenConsumption) * 1.2)
		estimate.HumanReviewPoints++
	}

	// 业务价值影响
	switch context.BusinessValue {
	case "高":
		estimate.HumanReviewPoints++
		estimate.RiskLevel = ce.escalateRisk(estimate.RiskLevel)
	case "低":
		estimate.HumanReviewPoints = max(0, estimate.HumanReviewPoints-1)
	}

	return estimate
}

// adjustForDescription 根据任务描述调整成本
func (ce *CostEstimator) adjustForDescription(estimate CostEstimate, description string) CostEstimate {
	desc := strings.ToLower(description)

	// 关键词影响
	highCostKeywords := []string{"重构", "架构", "性能", "安全", "集成", "迁移"}
	lowCostKeywords := []string{"注释", "文档", "格式", "错别字", "重命名"}

	for _, keyword := range highCostKeywords {
		if strings.Contains(desc, keyword) {
			estimate.TokenConsumption = int(float64(estimate.TokenConsumption) * 1.3)
			estimate.ToolCalls = int(float64(estimate.ToolCalls) * 1.2)
			break
		}
	}

	for _, keyword := range lowCostKeywords {
		if strings.Contains(desc, keyword) {
			estimate.TokenConsumption = int(float64(estimate.TokenConsumption) * 0.7)
			estimate.ToolCalls = int(float64(estimate.ToolCalls) * 0.8)
			break
		}
	}

	// 描述长度影响
	descriptionFactor := min(float64(len(description))/100.0, 2.0)
	estimate.TokenConsumption = int(float64(estimate.TokenConsumption) * descriptionFactor)

	return estimate
}

// escalateRisk 提升风险等级
func (ce *CostEstimator) escalateRisk(currentRisk string) string {
	switch currentRisk {
	case "低":
		return "中"
	case "中":
		return "高"
	default:
		return currentRisk
	}
}

// max 返回两个整数中的较大值
func max(a, b int) int {
	if a > b {
		return a
	}
	return b
}

// min 返回两个浮点数中的较小值
func min(a, b float64) float64 {
	if a < b {
		return a
	}
	return b
}

// FormatEstimate 格式化输出成本估算
func (ce *CostEstimator) FormatEstimate(estimate CostEstimate) string {
	return fmt.Sprintf(`💰 成本估算:
- Token消耗: %d
- 工具调用: %d次
- 人工审查点: %d个
- 预计完成时间: %s
- 风险评级: %s
- 业务价值: %s
- 估算理由: %s`,
		estimate.TokenConsumption,
		estimate.ToolCalls,
		estimate.HumanReviewPoints,
		estimate.EstimatedTime,
		estimate.RiskLevel,
		estimate.BusinessValue,
		estimate.Reasoning)
}

func main() {
	if len(os.Args) < 2 {
		fmt.Println("用法: go run cost_estimator.go '<任务描述>' [上下文JSON]")
		fmt.Println("示例: go run cost_estimator.go '修改API添加新字段' '{\"complexity\":\"simple\",\"domain\":\"backend\"}'")
		os.Exit(1)
	}

	taskDescription := os.Args[1]

	// 解析上下文
	var context TaskContext
	if len(os.Args) > 2 {
		if err := json.Unmarshal([]byte(os.Args[2]), &context); err != nil {
			log.Printf("警告: 上下文参数格式错误，使用默认值: %v", err)
		}
	}

	// 设置默认值
	if context.Complexity == "" {
		context.Complexity = "simple"
	}
	if context.Domain == "" {
		context.Domain = "general"
	}

	// 创建估算器并执行估算
	estimator := NewCostEstimator()
	estimate := estimator.EstimateCost(taskDescription, context)

	// 输出结果
	fmt.Println("=" * 60)
	fmt.Println("💰 任务成本估算")
	fmt.Println("=" * 60)
	fmt.Printf("任务描述: %s\n", taskDescription)
	fmt.Println(estimator.FormatEstimate(estimate))
	fmt.Println("=" * 60)

	// 输出JSON格式（供其他工具使用）
	jsonOutput, _ := json.MarshalIndent(estimate, "", "  ")
	fmt.Printf("\n📊 JSON输出:\n%s\n", jsonOutput)
}
