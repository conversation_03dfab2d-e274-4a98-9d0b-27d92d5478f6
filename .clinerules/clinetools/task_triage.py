#!/usr/bin/env python3
"""
Task Triage Tool - 任务分诊工具
基于 revise_20250702.md 的流程治理升级

实现任务复杂度分诊机制，使流程的严格程度与任务的复杂度及商业价值相匹配。
"""

import re
import sys
import json
from enum import Enum
from dataclasses import dataclass
from typing import List, Dict, Optional

class TaskComplexity(Enum):
    TRIVIAL = "trivial"      # 第1级：微小任务
    SIMPLE = "simple"        # 第2级：简单任务  
    COMPLEX = "complex"      # 第3级：复杂任务

class WorkflowPath(Enum):
    FAST_TRACK = "fast_track"        # 快速通道
    LIGHTWEIGHT = "lightweight"     # 轻量工作流
    STANDARD = "standard"           # 标准工作流

@dataclass
class TaskAnalysis:
    complexity: TaskComplexity
    workflow_path: WorkflowPath
    estimated_tokens: int
    estimated_tool_calls: int
    human_review_points: int
    business_value: str
    reasoning: str
    recommended_actions: List[str]

class TaskTriageEngine:
    """任务分诊引擎"""
    
    def __init__(self):
        # 微小任务关键词
        self.trivial_keywords = [
            "修改注释", "修复错别字", "更新文档", "格式化", "重命名变量",
            "fix typo", "update comment", "format code", "rename"
        ]
        
        # 简单任务关键词
        self.simple_keywords = [
            "添加字段", "修改配置", "简单修复", "单元测试", "API参数",
            "add field", "modify config", "simple fix", "unit test"
        ]
        
        # 复杂任务关键词
        self.complex_keywords = [
            "新模块", "重构", "架构", "设计", "集成", "性能优化",
            "new module", "refactor", "architecture", "design", "integration"
        ]

    def analyze_task(self, task_description: str, context: Dict = None) -> TaskAnalysis:
        """分析任务复杂度并推荐工作流路径"""
        
        task_lower = task_description.lower()
        
        # 1. 基于关键词的初步分类
        complexity = self._classify_by_keywords(task_lower)
        
        # 2. 基于上下文的调整
        if context:
            complexity = self._adjust_by_context(complexity, context)
        
        # 3. 确定工作流路径
        workflow_path = self._determine_workflow_path(complexity)
        
        # 4. 估算成本
        costs = self._estimate_costs(complexity, task_description)
        
        # 5. 生成建议
        analysis = TaskAnalysis(
            complexity=complexity,
            workflow_path=workflow_path,
            estimated_tokens=costs['tokens'],
            estimated_tool_calls=costs['tool_calls'],
            human_review_points=costs['review_points'],
            business_value="待确认",
            reasoning=self._generate_reasoning(complexity, task_description),
            recommended_actions=self._generate_recommendations(workflow_path)
        )
        
        return analysis

    def _classify_by_keywords(self, task_lower: str) -> TaskComplexity:
        """基于关键词分类任务复杂度"""
        
        # 检查微小任务关键词
        for keyword in self.trivial_keywords:
            if keyword in task_lower:
                return TaskComplexity.TRIVIAL
        
        # 检查复杂任务关键词
        for keyword in self.complex_keywords:
            if keyword in task_lower:
                return TaskComplexity.COMPLEX
        
        # 检查简单任务关键词
        for keyword in self.simple_keywords:
            if keyword in task_lower:
                return TaskComplexity.SIMPLE
        
        # 默认为简单任务
        return TaskComplexity.SIMPLE

    def _adjust_by_context(self, initial_complexity: TaskComplexity, context: Dict) -> TaskComplexity:
        """基于上下文调整复杂度"""
        
        # 如果涉及多个文件，提升复杂度
        if context.get('file_count', 0) > 5:
            if initial_complexity == TaskComplexity.TRIVIAL:
                return TaskComplexity.SIMPLE
            elif initial_complexity == TaskComplexity.SIMPLE:
                return TaskComplexity.COMPLEX
        
        # 如果是生产环境，提升复杂度
        if context.get('environment') == 'production':
            if initial_complexity == TaskComplexity.TRIVIAL:
                return TaskComplexity.SIMPLE
        
        return initial_complexity

    def _determine_workflow_path(self, complexity: TaskComplexity) -> WorkflowPath:
        """确定工作流路径"""
        
        if complexity == TaskComplexity.TRIVIAL:
            return WorkflowPath.FAST_TRACK
        elif complexity == TaskComplexity.SIMPLE:
            return WorkflowPath.LIGHTWEIGHT
        else:
            return WorkflowPath.STANDARD

    def _estimate_costs(self, complexity: TaskComplexity, task_description: str) -> Dict[str, int]:
        """估算任务成本"""
        
        base_costs = {
            TaskComplexity.TRIVIAL: {'tokens': 500, 'tool_calls': 2, 'review_points': 0},
            TaskComplexity.SIMPLE: {'tokens': 2000, 'tool_calls': 8, 'review_points': 1},
            TaskComplexity.COMPLEX: {'tokens': 8000, 'tool_calls': 25, 'review_points': 3}
        }
        
        costs = base_costs[complexity].copy()
        
        # 基于任务描述长度调整
        description_factor = min(len(task_description) / 100, 2.0)
        costs['tokens'] = int(costs['tokens'] * description_factor)
        
        return costs

    def _generate_reasoning(self, complexity: TaskComplexity, task_description: str) -> str:
        """生成分诊推理"""
        
        reasoning_templates = {
            TaskComplexity.TRIVIAL: "任务涉及简单的文本修改或格式调整，不涉及逻辑变更，风险极低。",
            TaskComplexity.SIMPLE: "任务涉及有限的功能修改，影响范围可控，需要基本的测试验证。",
            TaskComplexity.COMPLEX: "任务涉及重大功能开发或架构变更，需要完整的设计、实现和验证流程。"
        }
        
        return reasoning_templates[complexity]

    def _generate_recommendations(self, workflow_path: WorkflowPath) -> List[str]:
        """生成推荐行动"""
        
        recommendations = {
            WorkflowPath.FAST_TRACK: [
                "直接执行修改",
                "使用LOG_ACTION记录变更",
                "完全绕过Phases 0-3",
                "预计完成时间: 5-10分钟"
            ],
            WorkflowPath.LIGHTWEIGHT: [
                "合并Phase 1和2",
                "快速生成代码和必要的单元测试",
                "简化文档要求",
                "预计完成时间: 30-60分钟"
            ],
            WorkflowPath.STANDARD: [
                "严格遵循01_operational_workflow.md的每一个阶段",
                "完整的Phase 0-3流程",
                "全面的文档和测试要求",
                "预计完成时间: 2-8小时"
            ]
        }
        
        return recommendations[workflow_path]

def main():
    """命令行接口"""
    
    if len(sys.argv) < 2:
        print("用法: python task_triage.py '<任务描述>'")
        print("示例: python task_triage.py '修改API添加新的可选字段'")
        sys.exit(1)
    
    task_description = sys.argv[1]
    
    # 可选的上下文参数
    context = {}
    if len(sys.argv) > 2:
        try:
            context = json.loads(sys.argv[2])
        except json.JSONDecodeError:
            print("警告: 上下文参数格式错误，忽略")
    
    # 执行分诊
    engine = TaskTriageEngine()
    analysis = engine.analyze_task(task_description, context)
    
    # 输出结果
    print("=" * 60)
    print("🎯 任务分诊结果")
    print("=" * 60)
    print(f"任务描述: {task_description}")
    print(f"复杂度级别: {analysis.complexity.value.upper()}")
    print(f"推荐工作流: {analysis.workflow_path.value.upper()}")
    print(f"预估Token消耗: {analysis.estimated_tokens}")
    print(f"预估工具调用: {analysis.estimated_tool_calls}")
    print(f"人工审查点: {analysis.human_review_points}")
    print(f"分诊推理: {analysis.reasoning}")
    print("\n📋 推荐行动:")
    for i, action in enumerate(analysis.recommended_actions, 1):
        print(f"  {i}. {action}")
    print("=" * 60)

if __name__ == "__main__":
    main()
