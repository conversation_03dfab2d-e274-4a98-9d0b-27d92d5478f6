# Directory Structure Constitution (目录结构宪法) v4.0

**文档ID**: DSC-4.0-CN
**权威级别**: 宪法级 (高于所有其他规则)
**日期**: 2025-07-02
**状态**: 强制执行
**基于**: revise_20250702.md 反思治理升级

## 🏛️ 宪法声明

本文档作为 .clinerules 框架的"宪法"，定义了**强制性的、统一的目录结构规约**。其权威性高于所有其他流程和规则。任何偏离都需在 `decision_log.md` 中记录理由。

### 治理原则
- **物理秩序**: 为所有产出物（代码、文档、日志、规约）提供明确、可预测的"家"
- **职责分离**: 严格禁止代码与文档混杂，解决文档存放混乱问题
- **可预测性**: 任何开发者都能在3秒内定位所需资源
- **强制性**: 100%合规，无例外，彻底根除物理存放的混乱

---

## 1. 框架自身目录结构 (.clinerules/) - v4.0升级

框架自身的规范性是所有项目规范性的基础。

```
.clinerules/
├── 00_agent_bootstrap.md          # 核心启动文档 (植入任务分诊逻辑)
├── 01_operational_workflow.md     # 操作工作流 (适用于复杂任务)
├── 02_phase_gate_checklist.md     # 阶段门检查清单
├── 03_systematic_review.md        # 系统性审查
├── 04_reflective_questions.md     # 反思性问题
├── 05_directory_structure.md      # 目录结构宪法 (本文档)
├── 06_documentation_standards.md  # 文档标准
├── 07_code_quality_standards.md   # 代码质量标准
├── 08_testing_standards.md        # 测试标准
├── 09_deployment_standards.md     # 部署标准
├── 10_security_standards.md       # 安全标准
├── 11_human_interaction_patterns.md # 人机交互模式 (新增)
├── rules/                          # 领域特定规则
│   ├── architecture.md
│   ├── backend_go.md
│   ├── database.md
│   ├── testing.md
│   └── deployment.md
├── clinetools/                     # 自动化工具 (新增)
│   ├── task_triage.py             # 任务分诊工具
│   ├── cost_estimator.go          # 成本估算工具
│   └── compliance_checker.sh      # 合规性检查工具
└── verification_harness/           # 验证工具套件
    ├── validate_project_structure.sh
    ├── quick_validate.sh
    └── project_template/
```

### 1.1 新增目录说明
- **`/clinetools/`**: 实现"工具即服务"，存放可执行的自动化脚本
- **`11_human_interaction_patterns.md`**: 人机协作的"剧本"，包含意图漏斗和决策强制模式

---

## 2. 标准项目目录结构 (强制性) - v4.0宪法级规约

所有受本框架治理的项目，**必须**遵循以下目录结构。任何偏离都需在`decision_log.md`中记录理由。

### 2.1 根目录 (/) - 项目元数据层
**存放内容**: 仅保留项目级定义文件和依赖管理文件
**治理要点**: 顶层清晰，避免文件杂乱，解决根目录文档过多问题

| 文件 | 用途 | 强制性 |
|------|------|--------|
| `{project_name}.intent.yaml` | 项目意图和用户故事 | ✅ 强制 |
| `deployment.config.toml` | 部署和环境配置 | ✅ 强制 |
| `decision_log.md` | 重要架构决策记录 | ✅ 强制 |
| `project_tech_debt_ledger.md` | 技术债务跟踪 | ✅ 强制 |
| `README.md` | 项目入口和快速开始 | ✅ 强制 |
| `go.mod`, `go.sum` | Go依赖管理 | ✅ 强制 |
| `Makefile` | 构建脚本 | 🔶 推荐 |
| `.env.example` | 环境变量模板 | 🔶 推荐 |

**🚫 禁止内容**:
- ❌ 任何 `*_REPORT.md` 报告文档 (应放在 `/plans/`)
- ❌ 任何 `*.log` 日志文件 (应放在 `/plans/`)
- ❌ 任何临时文件或测试文件
- ❌ 超过10个`.md`文档

### 2.2 代码层目录结构

| 目录 | 存放内容 | 治理要点与解决的问题 |
|------|----------|---------------------|
| **`/cmd/`** | 应用程序的可执行入口 (`main`包) | **职责分离**: 严格禁止在此处放置任何业务逻辑，仅用于启动程序 |
| **`/internal/`** | 所有私有的Go应用和业务逻辑代码 | **代码归宿**: 这是项目核心功能的"家"，与文档和规约物理隔离 |
| **`/pkg/`** | 公共库代码（可对外暴露） | **可复用性**: 存放可被其他项目引用的通用组件 |

#### `/internal/` 子目录结构 (Go项目标准)
```
/internal/
├── handlers/          # HTTP请求处理器
├── services/          # 业务逻辑服务
├── repositories/      # 数据访问层
├── models/           # 数据模型
├── middleware/       # 中间件
├── config/           # 配置管理
└── database/         # 数据库连接和迁移
```

### 2.3 规约和文档层目录结构

| 目录 | 存放内容 | 治理要点与解决的问题 |
|------|----------|---------------------|
| **`/specs/`** | 所有形式化规约文档（`*.spec.md`, `*.tla`） | **规约归宿**: 将"需求规约"从代码和设计文档中剥离，使其成为独立的、可验证的资产 |
| **`/plans/`** | 所有设计、规划、日志和分析报告 | **过程资产归宿**: 解决文档命名和存放混乱问题 |

#### `/plans/` 子目录结构 (强制性)
```
/plans/
├── architecture/      # 架构图与高阶设计
├── modules/          # 模块级详细设计
├── implementation/   # 实现计划文档
├── reports/          # 各种分析和验证报告
├── logs/            # 所有运行时日志必须存放于此
└── legacy/          # 历史文档和存档
```

**🎯 关键治理点**:
- **所有 `*_REPORT.md` 文档必须存放在 `/plans/reports/`**
- **所有 `*.log` 文件必须存放在 `/plans/logs/`**
- **所有 `plan_*.md` 文档必须存放在 `/plans/implementation/`**

### 2.4 支持层目录结构

| 目录 | 存放内容 | 治理要点 |
|------|----------|----------|
| **`/scripts/`** | 构建、测试、部署等辅助脚本 | **脚本归宿**: 统一管理所有自动化脚本 |
| **`/migrations/`** | 数据库迁移脚本 | **数据结构演进归宿**: 清晰管理数据库的生命周期 |
| **`/configs/`** | 配置文件 | **配置归宿**: 集中管理所有配置模板 |
| **`/tests/`** | 测试文件和测试套件 | **测试归宿**: 独立的测试资产管理 |

## 3. 合规性验证与执行机制

### 3.1 自动化验证工具
```bash
# 快速验证项目结构合规性
.clinerules/verification_harness/quick_validate.sh

# 完整验证项目结构合规性
.clinerules/verification_harness/validate_project_structure.sh

# 自动化合规性检查
.clinerules/clinetools/compliance_checker.sh
```

### 3.2 违规处理机制
1. **强制性违规**: 立即停止工作流，要求修正
2. **推荐性违规**: 记录警告，允许继续但需在`decision_log.md`中说明理由
3. **历史遗留**: 通过文档重组计划逐步修正

### 3.3 文档重组执行计划
基于当前项目状态，需要执行以下重组：

#### 立即执行 (Phase 1)
```bash
# 创建标准目录结构
mkdir -p plans/{reports,implementation,logs,legacy}
mkdir -p plans/architecture plans/modules

# 移动报告文档
mv *_REPORT.md plans/reports/
mv *_ANALYSIS.md plans/reports/
mv *_SUMMARY.md plans/reports/

# 移动实现计划
mv plan_*.md plans/implementation/
mv PHASE*.md plans/implementation/

# 移动日志文件
mv *.log plans/logs/
mv session*.log plans/logs/
```

#### 验证执行 (Phase 2)
```bash
# 验证重组结果
.clinerules/verification_harness/quick_validate.sh

# 更新内部链接
# 更新README.md导航
# 更新所有文档间的引用
```

## 4. 治理效果与价值

### 4.1 解决的核心问题
- ✅ **文档存放混乱**: 通过强制性目录结构，每个文档都有明确的"家"
- ✅ **代码与文档混杂**: 物理隔离代码(`/internal/`)和文档(`/plans/`)
- ✅ **日志散乱**: 统一存放在`/plans/logs/`
- ✅ **规约难以定位**: 独立的`/specs/`目录

### 4.2 带来的价值
- 🎯 **可预测性**: 任何开发者都能在3秒内定位所需资源
- 🔧 **可维护性**: 清晰的职责分离便于项目维护
- 📊 **可审计性**: 标准化结构便于合规性检查
- 🚀 **可扩展性**: 为项目成长提供稳定的基础架构

## 5. 宪法执行声明

本宪法自发布之日起立即生效。所有现有项目必须在**7天内**完成合规性改造。所有新项目必须从第一天起100%遵循本宪法。

**违规后果**:
- 自动化工具将拒绝处理不合规的项目结构
- 所有工作流将在结构验证失败时停止
- 项目将被标记为"治理不合规"状态

**执行权威**: 本宪法的解释权和执行权归.clinerules框架核心治理委员会所有。
