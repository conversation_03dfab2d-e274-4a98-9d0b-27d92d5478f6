# Resumix MVP 项目模板

## 📋 **项目概述**

这是基于 .clinerules 框架的 Resumix MVP 项目模板，提供了标准化的项目结构和开发规范。

## 🏗️ **项目结构**

```
resumix/
├── resumix.intent.yaml              # 项目意图和用户故事
├── deployment.config.toml           # 部署和环境配置
├── decision_log.md                  # 重要架构决策记录
├── project_tech_debt_ledger.md      # 技术债务跟踪
├── README.md                        # 项目入口和快速开始
├── go.mod, go.sum                   # Go依赖管理
├── Makefile                         # 构建和任务脚本
│
├── cmd/                             # 命令行程序
│   ├── resumix-api/                 # API服务入口
│   └── resumix-worker/              # Worker服务入口
│
├── internal/                        # 内部包（不对外暴露）
│   ├── handlers/                    # HTTP请求处理器
│   ├── services/                    # 业务逻辑服务
│   ├── repositories/                # 数据访问层
│   ├── models/                      # 数据模型
│   ├── middleware/                  # 中间件
│   ├── config/                      # 配置管理
│   └── database/                    # 数据库连接和迁移
│
├── pkg/                             # 公共包（可对外暴露）
│   ├── llm/                         # LLM集成包
│   ├── logger/                      # 日志包
│   ├── redis/                       # Redis客户端
│   └── storage/                     # 存储抽象
│
├── specs/                           # 技术规范文档
│   ├── resumix.spec.md              # 完整技术规范
│   └── resumix.tla                  # TLA+形式化模型
│
├── plans/                           # 设计和计划文档
│   ├── architecture/                # 架构设计文档
│   ├── implementation/              # 实现计划文档
│   └── modules/                     # 模块设计文档
│
├── docs/                            # 项目文档（推荐结构）
│   ├── reports/                     # 各种分析和验证报告
│   ├── guides/                      # 部署、开发、配置指南
│   ├── analysis/                    # 设计分析和比较文档
│   └── legacy/                      # 历史文档和存档
│
├── configs/                         # 配置文件
│   ├── default_profile.yaml         # 默认配置
│   └── default_tarinn_strategy.md   # 默认策略配置
│
├── scripts/                         # 脚本文件
│   ├── deploy-production.sh         # 生产部署脚本
│   ├── test-all.sh                  # 测试脚本
│   └── setup-wsl-network.ps1        # 开发环境设置
│
├── migrations/                      # 数据库迁移
│   ├── 001_create_adjudication_leads.up.sql
│   └── 001_create_adjudication_leads.down.sql
│
├── tests/                           # 测试文件
│   └── backend_test_suite.md        # 测试套件文档
│
├── k8s/                             # Kubernetes配置
│   ├── namespace.yaml
│   └── configmap.yaml
│
├── .clinerules/                     # 框架规则和工具
│   ├── rules/                       # 开发规则文档
│   ├── verification_harness/        # 验证工具
│   └── project_template/            # 项目模板
│
└── docker-compose.prod.yml          # 生产环境Docker配置
```

## 🚀 **快速开始**

### 1. 环境准备
```bash
# 安装Go 1.21+
go version

# 安装Docker和Docker Compose
docker --version
docker-compose --version

# 克隆项目
git clone <repository-url>
cd resumix
```

### 2. 依赖安装
```bash
# 安装Go依赖
go mod download

# 启动开发环境
make dev-setup
```

### 3. 配置环境
```bash
# 复制环境配置
cp .env.example .env

# 编辑配置文件
vim .env
```

### 4. 运行服务
```bash
# 启动数据库和Redis
docker-compose up -d postgres redis

# 运行数据库迁移
make migrate-up

# 启动API服务
make run-api

# 启动Worker服务
make run-worker
```

## 📚 **核心文档**

### **项目级文档** (根目录)
- `resumix.intent.yaml` - 项目意图和用户故事定义
- `deployment.config.toml` - 部署和环境配置
- `decision_log.md` - 重要架构决策记录
- `project_tech_debt_ledger.md` - 技术债务跟踪和管理
- `README.md` - 项目入口和快速开始指南

### **技术规范** (/specs/)
- `resumix.spec.md` - 完整的技术规范文档
- `resumix.tla` - TLA+形式化模型

### **设计文档** (/plans/)
- `/architecture/` - 系统架构设计文档
- `/implementation/` - 具体实现计划文档
- `/modules/` - 模块级设计文档

### **开发规范** (/.clinerules/rules/)
- `database.md` - 数据库开发规则
- `testing.md` - 测试开发规则
- `deployment.md` - 部署规则

## 🛠️ **开发工作流**

### 1. 功能开发
```bash
# 创建功能分支
git checkout -b feature/new-feature

# 开发和测试
make test
make lint

# 提交代码
git commit -m "feat: add new feature"
```

### 2. 代码审查
- 确保所有测试通过
- 遵循代码规范
- 更新相关文档
- 通过.clinerules验证

### 3. 部署流程
```bash
# 验证项目结构
.clinerules/verification_harness/validate_project_structure.sh

# 构建和部署
make build
make deploy-staging
```

## 🧪 **测试策略**

### 测试层次
- **单元测试**: 70% - 测试单个函数/方法
- **集成测试**: 20% - 测试组件间交互
- **端到端测试**: 10% - 测试完整用户流程

### 运行测试
```bash
# 运行所有测试
make test

# 运行单元测试
make test-unit

# 运行集成测试
make test-integration

# 生成覆盖率报告
make test-coverage
```

## 📦 **部署指南**

### 开发环境
```bash
# 使用Docker Compose
docker-compose up -d

# 或使用本地服务
make dev-setup
```

### 生产环境
```bash
# 使用Kubernetes
kubectl apply -f k8s/

# 或使用Docker
docker-compose -f docker-compose.prod.yml up -d
```

## 🔧 **配置管理**

### 环境变量
```bash
# 数据库配置
DB_HOST=localhost
DB_PORT=5432
DB_NAME=resumix
DB_USER=postgres
DB_PASSWORD=your_password

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379

# LLM配置
GEMINI_API_KEY=your_api_key
GEMINI_MODEL=gemini-pro
```

### 配置文件
- `configs/default_profile.yaml` - 应用默认配置
- `deployment.config.toml` - 部署相关配置
- `.env` - 环境变量配置

## 📊 **监控和日志**

### 健康检查
- API健康检查: `GET /health`
- 就绪检查: `GET /ready`
- 指标端点: `GET /metrics`

### 日志管理
- 结构化日志输出
- 不同级别的日志记录
- 生产环境日志聚合

## 🔒 **安全考虑**

### 认证和授权
- JWT令牌认证
- 基于角色的访问控制
- API密钥管理

### 数据安全
- 敏感数据加密
- 安全的密钥管理
- 数据备份和恢复

## 🤝 **贡献指南**

### 开发规范
1. 遵循.clinerules框架规范
2. 编写清晰的提交信息
3. 添加适当的测试覆盖
4. 更新相关文档

### 代码审查
- 功能正确性
- 代码质量
- 测试覆盖率
- 文档完整性

## 📞 **支持和联系**

- 项目文档: `/docs/`
- 技术规范: `/specs/resumix.spec.md`
- 问题跟踪: GitHub Issues
- 技术债务: `project_tech_debt_ledger.md`

## 📄 **许可证**

[在此添加许可证信息]

---

**注意**: 这是一个基于.clinerules框架的标准化项目模板，确保项目结构和开发流程的一致性和可维护性。
