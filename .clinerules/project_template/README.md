# Project: Telegram RP Bot v2.0
# Last Updated: [YYYY-MM-DD HH:MM:SS]

## 1. Project Mission
To create a modular, scalable, and engaging roleplaying bot on Telegram, following the LeadAgent Cognitive Framework v2.1.

## 2. Core Project Artifacts (Quick Links)
*   **Intent & Spec:** [`rp_bot.intent.yaml`](./rp_bot.intent.yaml), [`rp_bot.spec.md`](./rp_bot.spec.md)
*   **Architecture Decisions:** [`decision_log.md`](./decision_log.md)
*   **Deployment Config:** [`deployment.config.toml`](./deployment.config.toml)
*   **Current Active Plan:** [`plan_user_auth_module.md`](./plans/plan_user_auth_module.md)
*   **Tech Debt Ledger:** [`project_tech_debt_ledger.md`](./project_tech_debt_ledger.md)

## 3. Current Status & Next Steps
*   **Overall Progress:** 25%
*   **Current Phase (Workflow):** Phase 2 - Logic Generation & Verification
*   **Active Task:** Implementing the `User Authentication` module.
*   **Next Task:** Design the `Character Management` module.

## 4. How to Start a Development Session (for Human & AI)
1.  **Select a task** from an active `plan_*.md` file.
2.  **Create a new session log:** `cp session.log.template.md logs/sessions/YYYYMMDD_[task_name].log`
3.  **Fill the session log header:** Define the task goal, context, and key rules.
4.  **Initiate the AI Agent:** Provide the path to the newly created session log as the primary context.
5.  **Begin work** by following the `01_operational_workflow.md`.