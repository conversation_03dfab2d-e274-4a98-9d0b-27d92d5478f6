# Proactive Verification Standards (主动验证标准) v1.0

**目的**: 定义 LLM 应该何时、如何主动进行自我检查和完整性验证，避免需要用户反复提示才发现和修复问题。

## 1. 主动验证触发条件

### 1.1. 强制触发条件 (MUST Trigger)

LLM **必须**在以下情况下主动进行完整性验证：

*   **阶段声明完成时**: 在声明任何阶段完成之前，必须执行对应的 Phase Gate Checklist
*   **复杂任务完成时**: 当完成涉及 5+ 文件或 10+ 函数的任务时
*   **架构变更后**: 当对系统架构进行任何修改后
*   **错误修复后**: 当修复任何错误或问题后
*   **用户质疑时**: 当用户对完整性或质量提出质疑时

### 1.2. 建议触发条件 (SHOULD Trigger)

LLM **应该**在以下情况下考虑进行验证：

*   **长时间工作后**: 连续工作 30+ 分钟或 20+ 工具调用后
*   **复杂逻辑实现后**: 实现包含复杂状态机、错误处理或并发控制的逻辑后
*   **集成点创建后**: 创建或修改系统间集成点后
*   **性能关键代码后**: 实现性能关键路径的代码后

### 1.3. 可选触发条件 (MAY Trigger)

LLM **可以**在以下情况下进行验证：

*   **用户暂停时**: 用户长时间未响应，可以利用时间进行自我检查
*   **不确定时**: 对自己的工作质量不确定时
*   **学习机会时**: 发现可以改进工作流程的机会时

## 2. 验证深度标准和具体检查清单

### 2.1. 系统复杂度分级

#### **简单系统** (≤50 函数, ≤10 错误类型)
*   **基础验证**: 检查核心功能是否实现
*   **文档验证**: 确保基本文档存在且一致
*   **接口验证**: 验证主要接口定义正确

#### **中等系统** (51-200 函数, 11-50 错误类型)
*   **功能验证**: 检查所有主要功能路径
*   **错误处理验证**: 验证主要错误情况有处理
*   **集成验证**: 检查组件间集成点
*   **性能验证**: 验证关键性能路径

#### **复杂系统** (≥201 函数, ≥51 错误类型)
*   **全面功能验证**: 检查所有功能和边缘案例
*   **完整错误处理验证**: 验证所有错误类型和恢复策略
*   **深度集成验证**: 检查所有组件交互和数据流
*   **性能和并发验证**: 验证性能优化和并发安全
*   **状态管理验证**: 验证复杂状态机和一致性

### 2.2. 文档逻辑贯通性验证标准

#### **2.2.1. 横向一致性检查**
**目的**: 确保同一层级的文档之间逻辑一致，没有矛盾或重复

**检查项目**:
*   `[ ]` **接口一致性**: 服务间接口定义在所有相关文档中保持一致
*   `[ ]` **数据模型一致性**: 同一实体在不同文档中的定义完全一致
*   `[ ]` **错误处理一致性**: 相同错误在不同组件中的处理策略一致
*   `[ ]` **状态定义一致性**: 状态机状态在所有相关文档中定义一致
*   `[ ]` **业务规则一致性**: 业务逻辑在不同层级中的实现保持一致

**验证方法**:
```
1. 提取所有接口定义，检查签名、参数、返回值一致性
2. 提取所有数据模型，验证字段定义、类型、约束一致性
3. 提取所有错误处理，确保相同错误的处理策略统一
4. 提取所有状态定义，验证状态转换规则一致性
5. 提取所有业务规则，确保跨组件实现的一致性
```

#### **2.2.2. 纵向依赖性检查**
**目的**: 确保上下层文档之间的依赖关系正确，调用链完整

**检查项目**:
*   `[ ]` **调用链完整性**: 从用户故事到具体实现的调用链无断裂
*   `[ ]` **依赖关系正确性**: 组件间依赖关系在所有层级中正确反映
*   `[ ]` **数据流连续性**: 数据在不同层级间的流转路径完整
*   `[ ]` **错误传播正确性**: 错误在调用链中的传播和处理正确
*   `[ ]` **事务边界一致性**: 事务边界在不同层级中的定义一致

**验证方法**:
```
1. 追踪每个用户故事的完整实现路径
2. 验证每个组件的依赖在被依赖组件中有对应实现
3. 追踪数据从输入到输出的完整流转路径
4. 验证错误在调用链中的正确传播和处理
5. 检查事务边界在不同层级中的一致性定义
```

#### **2.2.3. 完整性覆盖检查**
**目的**: 确保所有必需的功能、场景、组件都有完整的文档覆盖

**检查项目**:
*   `[ ]` **功能覆盖完整性**: 所有用户故事都有对应的实现文档
*   `[ ]` **场景覆盖完整性**: 所有 Gherkin 场景都有对应的逻辑实现
*   `[ ]` **组件覆盖完整性**: 代码结构中的所有组件都有逻辑文档
*   `[ ]` **集成点覆盖完整性**: 所有外部集成都有详细的逻辑规范
*   `[ ]` **错误场景覆盖完整性**: 所有识别的错误类型都有处理逻辑

**验证方法**:
```
1. 对比 .intent.yaml 和实现文档，确保功能覆盖完整
2. 对比 .spec.md 和逻辑文档，确保场景覆盖完整
3. 对比 code_structure.md 和逻辑文档，确保组件覆盖完整
4. 检查所有外部依赖都有对应的集成逻辑文档
5. 验证错误处理策略文档覆盖所有识别的错误类型
```

### 2.3. 系统性验证检查清单

#### **2.3.1. 文档存在性和结构检查**
*   `[ ]` **必需文档存在性**: 所有 Phase Gate 要求的文档都存在
*   `[ ]` **文档结构完整性**: 每个文档都有完整的章节结构
*   `[ ]` **索引文档同步性**: 概览文档正确索引所有实际文档
*   `[ ]` **文档版本一致性**: 所有文档的版本信息和更新时间一致
*   `[ ]` **文档命名规范性**: 文件命名遵循既定的命名规范

#### **2.3.2. 逻辑实现完整性检查**
*   `[ ]` **函数签名完整性**: 所有声明的函数都有详细的实现逻辑
*   `[ ]` **参数验证完整性**: 所有函数都有输入参数验证逻辑
*   `[ ]` **返回值处理完整性**: 所有函数都有明确的返回值处理逻辑
*   `[ ]` **异常路径完整性**: 所有可能的异常情况都有处理逻辑
*   `[ ]` **状态转换完整性**: 所有状态机转换都有详细的实现逻辑

#### **2.3.3. 业务逻辑一致性检查**
*   `[ ]` **用户故事映射完整性**: 每个用户故事都能追踪到具体实现
*   `[ ]` **Gherkin 场景实现完整性**: 每个场景都有对应的逻辑实现
*   `[ ]` **业务规则一致性**: 相同业务规则在不同组件中实现一致
*   `[ ]` **数据验证规则一致性**: 数据验证在不同层级中保持一致
*   `[ ]` **权限控制一致性**: 权限检查在所有相关组件中一致

#### **2.3.4. 技术实现一致性检查**
*   `[ ]` **接口定义一致性**: 相同接口在不同文档中定义完全一致
*   `[ ]` **数据模型一致性**: 相同实体在所有文档中结构一致
*   `[ ]` **错误处理策略一致性**: 相同错误类型的处理策略统一
*   `[ ]` **事务边界一致性**: 事务范围在不同层级中定义一致
*   `[ ]` **并发控制一致性**: 并发处理策略在相关组件中一致

#### **2.3.5. 集成和依赖检查**
*   `[ ]` **组件依赖完整性**: 所有依赖的组件都有对应的实现
*   `[ ]` **外部集成完整性**: 所有外部服务集成都有详细逻辑
*   `[ ]` **数据流连续性**: 数据在组件间流转路径完整无断裂
*   `[ ]` **调用链完整性**: 从入口到出口的调用链完整可追踪
*   `[ ]` **配置依赖完整性**: 所有配置项都有对应的使用逻辑

#### **2.3.6. 质量和性能检查**
*   `[ ]` **错误处理覆盖完整性**: 所有错误类型都有处理策略
*   `[ ]` **性能优化嵌入性**: 关键路径都有性能优化考虑
*   `[ ]` **安全措施完整性**: 所有安全要求都有对应实现
*   `[ ]` **监控和日志完整性**: 所有关键操作都有监控和日志
*   `[ ]` **可维护性标准符合性**: 代码结构和文档符合可维护性要求

## 3. 系统性审查执行流程

### 3.1. 审查执行步骤

```
1. **触发识别**: 识别当前情况是否满足触发条件
2. **范围确定**: 根据系统复杂度确定检查深度
3. **文档清单验证**: 确认所有必需文档存在
4. **逻辑贯通性检查**: 执行横向一致性和纵向依赖性验证
5. **完整性覆盖检查**: 验证功能、场景、组件覆盖完整性
6. **质量标准验证**: 检查实现质量和技术标准符合性
7. **问题识别和分类**: 记录发现的所有问题并按严重程度分类
8. **修复优先级排序**: 按照影响程度和修复难度排序
9. **修复计划制定**: 制定详细的问题修复行动计划
10. **执行修复措施**: 实施修复并验证结果
11. **交叉验证**: 确认修复没有引入新问题
12. **报告生成**: 生成详细的审查报告和修复记录
```

### 3.2. 具体审查工具和方法

#### **3.2.1. 文档存在性验证工具**
```
DOCUMENT_EXISTENCE_CHECK(project_root, required_documents_list):
    missing_documents = []
    for document in required_documents_list:
        if not file_exists(document):
            missing_documents.append(document)
    return missing_documents

DOCUMENT_STRUCTURE_CHECK(document_path, required_sections):
    missing_sections = []
    document_content = read_file(document_path)
    for section in required_sections:
        if not section_exists(document_content, section):
            missing_sections.append(section)
    return missing_sections
```

#### **3.2.2. 逻辑一致性验证工具**
```
INTERFACE_CONSISTENCY_CHECK(documents_list):
    interfaces = extract_all_interfaces(documents_list)
    inconsistencies = []
    for interface_name in interfaces:
        definitions = interfaces[interface_name]
        if not all_definitions_identical(definitions):
            inconsistencies.append({
                'interface': interface_name,
                'conflicting_definitions': definitions
            })
    return inconsistencies

DATA_MODEL_CONSISTENCY_CHECK(documents_list):
    models = extract_all_data_models(documents_list)
    inconsistencies = []
    for model_name in models:
        definitions = models[model_name]
        if not all_definitions_identical(definitions):
            inconsistencies.append({
                'model': model_name,
                'conflicting_definitions': definitions
            })
    return inconsistencies
```

#### **3.2.3. 覆盖完整性验证工具**
```
FUNCTIONAL_COVERAGE_CHECK(intent_file, implementation_docs):
    user_stories = extract_user_stories(intent_file)
    implemented_functions = extract_implemented_functions(implementation_docs)

    missing_implementations = []
    for story in user_stories:
        if not has_implementation(story, implemented_functions):
            missing_implementations.append(story)

    return missing_implementations

SCENARIO_COVERAGE_CHECK(spec_file, logic_docs):
    scenarios = extract_gherkin_scenarios(spec_file)
    logic_implementations = extract_logic_implementations(logic_docs)

    missing_logic = []
    for scenario in scenarios:
        if not has_logic_implementation(scenario, logic_implementations):
            missing_logic.append(scenario)

    return missing_logic
```

### 3.3. 系统性审查报告模板

```markdown
## 系统性审查验证报告

**项目**: [项目名称]
**审查类型**: [文档完整性审查/逻辑贯通性审查/质量标准审查]
**触发条件**: [描述触发验证的原因]
**系统复杂度**: [简单/中等/复杂系统]
**审查范围**: [具体审查的文档和组件范围]
**审查时间**: [审查开始和结束时间]
**审查者**: [执行审查的角色]

### 1. 审查执行摘要
**总体状态**: [通过/部分通过/失败]
**检查项总数**: [总检查项数量]
**通过项数量**: [通过的检查项数量]
**失败项数量**: [失败的检查项数量]
**警告项数量**: [需要注意的项目数量]

### 2. 详细检查结果

#### 2.1. 文档存在性和结构检查
- ✅ **通过项**: [列出通过的检查项]
- ❌ **失败项**: [列出失败的检查项]
- ⚠️ **警告项**: [列出需要注意的项目]

#### 2.2. 逻辑贯通性检查
- ✅ **横向一致性**: [接口、数据模型、错误处理一致性结果]
- ✅ **纵向依赖性**: [调用链、依赖关系、数据流连续性结果]
- ❌ **发现的不一致**: [具体的不一致问题]

#### 2.3. 完整性覆盖检查
- ✅ **功能覆盖**: [用户故事到实现的覆盖情况]
- ✅ **场景覆盖**: [Gherkin场景到逻辑的覆盖情况]
- ✅ **组件覆盖**: [代码结构到逻辑文档的覆盖情况]
- ❌ **覆盖缺失**: [具体的覆盖缺失项目]

#### 2.4. 质量标准检查
- ✅ **实现质量**: [错误处理、性能、安全性检查结果]
- ✅ **技术标准**: [架构一致性、接口规范检查结果]
- ❌ **质量问题**: [具体的质量问题]

### 3. 问题分析和分类

#### 3.1. 严重问题 (Critical)
1. **[问题类型]**: [问题描述]
   - **影响范围**: [问题影响的组件和功能]
   - **严重程度**: [对系统整体的影响程度]
   - **根本原因**: [问题的根本原因分析]
   - **修复优先级**: [修复的紧急程度]

#### 3.2. 重要问题 (Major)
[同上格式]

#### 3.3. 一般问题 (Minor)
[同上格式]

### 4. 修复行动计划

#### 4.1. 立即修复 (Immediate)
- [ ] **[问题描述]**: [具体修复措施和预期完成时间]

#### 4.2. 短期修复 (Short-term)
- [ ] **[问题描述]**: [具体修复措施和预期完成时间]

#### 4.3. 长期改进 (Long-term)
- [ ] **[问题描述]**: [具体改进措施和预期完成时间]

### 5. 修复执行记录

#### 5.1. 已完成修复
- [x] **[问题描述]**: [修复措施和完成时间]
  - **修复验证**: [修复效果验证结果]
  - **副作用检查**: [是否引入新问题]

#### 5.2. 进行中修复
- [/] **[问题描述]**: [当前进度和预期完成时间]

### 6. 交叉验证结果
**修复后重新检查结果**: [修复后的整体检查状态]
**新引入问题**: [修复过程中是否引入新问题]
**整体改进效果**: [修复前后的对比分析]

### 7. 质量保证建议

#### 7.1. 流程改进建议
[对审查流程的改进建议]

#### 7.2. 标准完善建议
[对质量标准的完善建议]

#### 7.3. 工具优化建议
[对审查工具的优化建议]

### 8. 最终授权状态
**审查结论**: [通过/条件通过/不通过]
**授权进入下一阶段**: [是/否]
**附加条件**: [如果是条件通过，列出需要满足的条件]

---
**审查完成时间**: [完成时间]
**下次审查计划**: [下次审查的时间和范围]
**审查记录存档**: [审查记录的存档位置]
```

## 4. 质量标准定义

### 4.1. 完整性标准

#### **功能完整性**
*   所有用户故事都有对应的实现或规约
*   所有主要功能路径都有测试场景
*   所有错误情况都有处理策略

#### **文档完整性**
*   所有必需文档都存在且最新
*   文档间没有矛盾或不一致
*   所有接口和API都有文档

#### **架构完整性**
*   所有组件都有明确定义
*   所有依赖关系都已识别
*   所有集成点都有规约

### 4.2. 质量标准

#### **可测试性**
*   所有功能都可以通过自动化测试验证
*   所有错误情况都有测试用例
*   所有性能要求都有验证方法

#### **可维护性**
*   代码结构清晰，职责分离
*   文档充分，易于理解
*   修改影响范围可控

#### **健壮性**
*   错误处理全面且正确
*   边缘案例有适当处理
*   系统在异常情况下稳定

## 5. 实施指导

### 5.1. 日常实践

*   **工作节奏**: 每完成一个主要任务后进行快速检查
*   **阶段检查**: 每个阶段结束前进行全面检查
*   **质疑响应**: 用户质疑时立即进行相关检查

### 5.2. 检查工具

*   **Phase Gate Checklist**: 用于阶段完成验证
*   **文档一致性检查**: 验证文档间一致性
*   **功能覆盖分析**: 确保功能完整实现
*   **架构合规检查**: 验证架构一致性

### 5.3. 持续改进

*   **检查效果评估**: 定期评估检查的有效性
*   **标准更新**: 根据项目经验更新标准
*   **流程优化**: 持续优化检查流程

---

**核心原则**: LLM 应该是主动的质量守护者，而不是被动的执行者。通过系统性的自我验证，确保交付质量，减少用户的质量管理负担。
