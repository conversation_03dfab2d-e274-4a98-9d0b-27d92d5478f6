# LeadAgent Rules Engine (Version 3.2.0)
# Last Updated: 2025-07-07 17:50:00 (UTC)

## Tier 1: Core Behavioral Rules (Always Loaded)
These are universal rules governing the LLM's fundamental behavior.

*   **[LLM_CORE_01 | Plan-Driven Execution]:** For any complex task, a plan MUST be established and confirmed before execution. This includes analyzing dependencies and identifying all modification points for multi-file changes. (Refactored from LLM017, DS006, LLM013)
*   **[LLM_CORE_02 | Verification After Modification]:** After any modification to a state or artifact, the result MUST be verified against the intended changes. Report any discrepancies or file corruption immediately. (Refactored from LLM006)
*   **[LLM_CORE_02A | Proactive Completeness Verification]:** MUST proactively perform completeness verification according to `03_proactive_verification_standards.md` when trigger conditions are met. Do not wait for user prompts to identify and fix gaps or inconsistencies. (New)
*   **[LLM_CORE_02B | Systematic Review Execution]:** MUST execute systematic reviews according to `05_systematic_review_standards.md` when conducting comprehensive verification. Reviews must be thorough, consistent, and follow established standards for logic coherence and implementation readiness. (New)
*   **[LLM_CORE_02C | Functional Completeness Enforcement]:** MUST ensure functional completeness in Phase 2 implementations. Prohibit >20% TODO placeholders, require core business logic implementation, and mandate actual service implementations over nil placeholders. Trigger Phase 2.5 when completeness standards are not met. (New v3.1.0)
*   **[LLM_CORE_02D | Mandatory Code Implementation Validation]:** MUST execute code implementation validation tools before Phase 2 completion. Execute `bash .clinerules/verification_harness/code_implementation_validator.sh` and `python3 .clinerules/verification_harness/doc_code_consistency_checker.py .` and ensure "PASS" or "PARTIAL" status. "FAIL" status requires immediate remediation. (New v3.2.0)
*   **[LLM_CORE_03 | Error Recovery and Resilience]:** MUST implement error recovery mechanisms according to `07_error_recovery_standards.md`. Create checkpoints before major operations and execute rollback procedures when critical errors occur. (New)
*   **[LLM_CORE_04 | Performance and Efficiency]:** MUST optimize performance according to `08_performance_efficiency_standards.md`. Monitor token usage, tool call efficiency, and maintain performance within established benchmarks. (New)
*   **[LLM_CORE_05 | Collaboration and Handoff]:** MUST follow collaboration standards in `09_collaboration_handoff_standards.md` when working with multiple LLMs or during work transitions. Ensure complete information transfer and state synchronization. (New)
*   **[LLM_CORE_06 | Learning and Adaptation]:** MUST apply learning mechanisms from `10_learning_adaptation_framework.md`. Collect experience data, identify patterns, and contribute to framework evolution. (New)
*   **[LLM_CORE_03 | Principled Tool Use]:** Tools must be used precisely. Failures must be investigated. For repeated `replace_in_file` failures, consider `write_to_file` as a fallback. (Refactored from LLM005, LLM007, TU003, LLM016)
*   **[LLM_CORE_04 | Knowledge Before Action]:** Before generating an artifact, consult the Knowledge Base for existing patterns. Before proposing a design, consult architectural rules. (New)
*   **[LLM_CORE_05 | Efficient Operations]:** Avoid overly frequent, fragmented operations on the same file. Reduce the frequency of validation tool calls; run them after a set of related changes. (Refactored from LLM014, LLM015)
*   **[LLM_CORE_06 | Robust LLM Interaction]:** When calling LLMs, explicitly request JSON and preprocess the response. Implement user language detection and pass it to the prompt. Use Chain-of-Thought and Few-Shot examples to improve output quality. (Refactored from LLM008, LLM009, LLM019, LLM020)
*   **[LLM_CORE_07 | Code Generation Integrity]:** Include comments explaining bug fixes. Do not generate placeholder comments. Ensure Go template placeholders match the struct fields exactly. (Refactored from LLM002, LLM004, LLM018)
*   **[LLM_CORE_08 | Cautious & Factual Communication]:** Avoid using superlative, absolute, or overly optimistic language (e.g., "perfect", "complete", "unprecedented", "finally"). State facts and completed actions objectively. When assessing readiness, qualify the statement with the evidence at hand (e.g., "Based on the completion of all checklist items, we can now consider proceeding..."). The goal is to maintain a professional, objective, and trustworthy tone.
*   **[LLM_CORE_09 | API Contract Governance]:** MUST ensure strict adherence to API documentation conventions and systematic governance approaches to resolve implementation inconsistencies between frontend and backend. Before implementing API calls, verify endpoint existence in backend documentation. Use API path constants and type definitions. Implement contract validation and automated checking tools. (New v3.1.0)

---
## Tier 2: Domain-Specific Rule Sets (Loaded On-Demand)
These are pointers to specialized rule files, loaded based on the task context.

*   **Architecture & Design:** `rules/architecture.md`
*   **Go Backend:** `rules/backend_go.md`
*   **TypeScript Frontend:** `rules/frontend_ts.md`
*   **API Governance:** `rules/api_governance.md`
*   **Database Interaction:** `rules/database.md`
*   **Security:** `rules/security.md`
*   **Code Style:** `rules/code_style.md`
*   **Legacy System Analysis:** `rules/legacy_system_analysis.md`
*   **Phase 2.5 Implementation:** `rules/phase2_5_implementation.md` (New v3.1.0)
*   **API Consistency Management:** `rules/api_consistency.md` (New v3.3.0)
*   **Component Lifecycle Management:** `rules/component_lifecycle.md` (New v3.3.0)
*   **Documentation Synchronization:** `rules/documentation_sync.md` (New v3.3.0)
*   **Consistency Prevention Framework:** `rules/consistency_prevention_framework.md` (New v3.3.0)
