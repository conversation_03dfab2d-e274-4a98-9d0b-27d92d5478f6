# Systematic Review Standards (系统性审查标准) v1.0

**目的**: 定义系统性审查的具体执行标准，确保文档逻辑完整贯通，审查过程规范明细，质量标准统一执行。

## 1. 审查类型和适用场景

### 1.1. 文档完整性审查 (Document Completeness Review)

**适用场景**:
- Phase Gate 检查点
- 用户质疑文档完整性时
- 发现文档缺失或不一致时
- 项目里程碑验收时

**审查重点**:
- 所有必需文档是否存在
- 文档结构是否完整
- 索引和导航是否正确
- 版本控制是否一致

### 1.2. 逻辑贯通性审查 (Logic Coherence Review)

**适用场景**:
- 复杂系统设计完成后
- 多组件集成设计时
- 发现逻辑矛盾或不一致时
- 架构重大变更后

**审查重点**:
- 横向逻辑一致性
- 纵向依赖完整性
- 数据流连续性
- 调用链完整性

### 1.3. 实现准备度审查 (Implementation Readiness Review)

**适用场景**:
- 进入实现阶段前
- 用户要求验证实现准备度时
- 发现实现指导不明确时
- 质量标准验证时

**审查重点**:
- 实现逻辑明确性
- 错误处理完整性
- 性能要求嵌入性
- 安全措施完整性

## 2. 审查执行标准

### 2.1. 审查前准备标准

#### **2.1.1. 审查范围确定**
```
REVIEW_SCOPE_DEFINITION:
1. 确定审查类型 (完整性/贯通性/准备度)
2. 确定系统复杂度级别 (简单/中等/复杂)
3. 确定审查深度 (基础/标准/深度)
4. 确定审查文档范围
5. 确定审查时间预算
6. 确定审查成功标准
```

#### **2.1.2. 审查工具准备**
```
REVIEW_TOOLS_PREPARATION:
1. 文档存在性检查工具
2. 逻辑一致性验证工具
3. 覆盖完整性分析工具
4. 质量标准检查工具
5. 问题跟踪和报告工具
```

#### **2.1.3. 基准数据收集**
```
BASELINE_DATA_COLLECTION:
1. 收集所有相关文档列表
2. 提取所有接口定义
3. 提取所有数据模型定义
4. 提取所有错误处理策略
5. 提取所有业务规则定义
6. 建立可追溯性矩阵
```

### 2.2. 审查执行标准

#### **2.2.1. 系统性检查流程**
```
SYSTEMATIC_CHECK_PROCESS:
Phase 1: 文档存在性验证
  1.1. 检查所有必需文档存在
  1.2. 验证文档结构完整性
  1.3. 检查索引文档同步性
  1.4. 验证文档命名规范性

Phase 2: 逻辑一致性验证
  2.1. 横向一致性检查
    - 接口定义一致性
    - 数据模型一致性
    - 错误处理一致性
    - 业务规则一致性
  2.2. 纵向依赖性检查
    - 调用链完整性
    - 依赖关系正确性
    - 数据流连续性
    - 事务边界一致性

Phase 3: 覆盖完整性验证
  3.1. 功能覆盖检查
  3.2. 场景覆盖检查
  3.3. 组件覆盖检查
  3.4. 集成点覆盖检查

Phase 4: 质量标准验证
  4.1. 实现明确性检查
  4.2. 错误处理完整性检查
  4.3. 性能要求检查
  4.4. 安全措施检查
```

#### **2.2.2. 问题识别和分类标准**
```
ISSUE_IDENTIFICATION_STANDARDS:

严重问题 (Critical Issues):
- 缺失关键文档或组件
- 逻辑矛盾导致无法实现
- 安全漏洞或重大风险
- 架构不一致导致系统性问题

重要问题 (Major Issues):
- 功能覆盖不完整
- 错误处理策略缺失
- 性能要求未明确
- 接口定义不一致

一般问题 (Minor Issues):
- 文档格式不规范
- 注释或说明不充分
- 非关键路径的小问题
- 可优化但不影响功能的问题

改进建议 (Improvement Suggestions):
- 代码结构优化建议
- 文档组织改进建议
- 流程效率提升建议
- 工具使用优化建议
```

### 2.3. 审查质量保证标准

#### **2.3.1. 审查覆盖率要求**
```
REVIEW_COVERAGE_REQUIREMENTS:

复杂系统 (≥201 函数):
- 文档覆盖率: 100%
- 功能覆盖率: 100%
- 错误处理覆盖率: ≥95%
- 集成点覆盖率: 100%
- 性能关键路径覆盖率: 100%

中等系统 (51-200 函数):
- 文档覆盖率: 100%
- 功能覆盖率: ≥95%
- 错误处理覆盖率: ≥90%
- 集成点覆盖率: 100%
- 性能关键路径覆盖率: ≥90%

简单系统 (≤50 函数):
- 文档覆盖率: 100%
- 功能覆盖率: ≥90%
- 错误处理覆盖率: ≥80%
- 集成点覆盖率: 100%
- 性能关键路径覆盖率: ≥80%
```

#### **2.3.2. 审查质量验证标准**
```
REVIEW_QUALITY_VERIFICATION:

审查深度验证:
- 每个检查项都有明确的验证标准
- 每个问题都有详细的根因分析
- 每个修复建议都有具体的实施方案
- 每个质量标准都有量化的评判标准

审查一致性验证:
- 相同类型问题的评判标准一致
- 相同严重程度问题的处理方式一致
- 审查报告格式和内容标准一致
- 修复验证标准和方法一致

审查完整性验证:
- 所有计划检查项都已执行
- 所有发现问题都已记录和分类
- 所有修复措施都已验证效果
- 所有审查结果都已文档化
```

## 3. 审查工具和方法

### 3.1. 自动化检查工具

#### **3.1.1. 文档存在性检查工具**
```python
def document_existence_check(project_root, required_docs):
    """检查所有必需文档是否存在"""
    missing_docs = []
    for doc_path in required_docs:
        full_path = os.path.join(project_root, doc_path)
        if not os.path.exists(full_path):
            missing_docs.append(doc_path)
    return missing_docs

def document_structure_check(doc_path, required_sections):
    """检查文档结构完整性"""
    with open(doc_path, 'r') as f:
        content = f.read()
    
    missing_sections = []
    for section in required_sections:
        if section not in content:
            missing_sections.append(section)
    
    return missing_sections
```

#### **3.1.2. 逻辑一致性检查工具**
```python
def interface_consistency_check(docs_list):
    """检查接口定义一致性"""
    interfaces = {}
    inconsistencies = []
    
    for doc in docs_list:
        doc_interfaces = extract_interfaces(doc)
        for interface_name, definition in doc_interfaces.items():
            if interface_name in interfaces:
                if interfaces[interface_name] != definition:
                    inconsistencies.append({
                        'interface': interface_name,
                        'doc1': interfaces[interface_name]['source'],
                        'def1': interfaces[interface_name]['definition'],
                        'doc2': doc,
                        'def2': definition
                    })
            else:
                interfaces[interface_name] = {
                    'source': doc,
                    'definition': definition
                }
    
    return inconsistencies

def data_flow_continuity_check(flow_docs):
    """检查数据流连续性"""
    data_flows = extract_data_flows(flow_docs)
    broken_flows = []
    
    for flow in data_flows:
        if not is_flow_continuous(flow):
            broken_flows.append(flow)
    
    return broken_flows
```

### 3.2. 手工审查方法

#### **3.2.1. 逻辑贯通性手工审查**
```
MANUAL_LOGIC_REVIEW_PROCESS:

1. 选择关键用户故事
2. 追踪从用户故事到实现的完整路径
3. 验证每个步骤的逻辑连贯性
4. 检查异常处理的完整性
5. 验证数据转换的正确性
6. 确认业务规则的一致性实现

审查记录格式:
- 用户故事: [具体的用户故事]
- 追踪路径: [完整的实现路径]
- 逻辑检查: [每个步骤的逻辑验证结果]
- 发现问题: [具体的问题描述]
- 修复建议: [具体的修复建议]
```

#### **3.2.2. 质量标准手工审查**
```
MANUAL_QUALITY_REVIEW_PROCESS:

1. 选择关键功能组件
2. 检查错误处理的完整性和正确性
3. 验证性能要求的嵌入情况
4. 检查安全措施的实施情况
5. 验证可维护性标准的符合情况
6. 确认监控和日志的完整性

审查记录格式:
- 组件名称: [具体的组件名称]
- 质量维度: [错误处理/性能/安全/可维护性]
- 检查结果: [具体的检查结果]
- 符合程度: [完全符合/部分符合/不符合]
- 改进建议: [具体的改进建议]
```

## 4. 审查结果处理标准

### 4.1. 问题修复优先级标准

#### **4.1.1. 修复优先级矩阵**
```
优先级 = 严重程度 × 影响范围 × 修复紧急度

严重程度:
- Critical: 4分
- Major: 3分
- Minor: 2分
- Improvement: 1分

影响范围:
- 系统级: 4分
- 模块级: 3分
- 组件级: 2分
- 功能级: 1分

修复紧急度:
- 立即: 4分
- 短期: 3分
- 中期: 2分
- 长期: 1分

优先级分级:
- P0 (48-64分): 立即修复
- P1 (32-47分): 短期修复
- P2 (16-31分): 中期修复
- P3 (1-15分): 长期改进
```

### 4.2. 修复验证标准

#### **4.2.1. 修复效果验证**
```
REPAIR_VERIFICATION_STANDARDS:

1. 问题解决验证:
   - 原问题是否完全解决
   - 修复措施是否有效
   - 是否达到预期效果

2. 副作用检查:
   - 是否引入新的问题
   - 是否影响其他组件
   - 是否破坏现有功能

3. 质量标准符合性:
   - 修复后是否符合质量标准
   - 是否提升整体质量
   - 是否改善用户体验

4. 文档同步更新:
   - 相关文档是否同步更新
   - 修复记录是否完整
   - 审查报告是否更新
```

---

**核心原则**: 系统性审查应该是全面的、一致的、可重复的，确保每次审查都能发现真正的问题并提供有效的解决方案，从而持续提升项目质量和实施效率。
