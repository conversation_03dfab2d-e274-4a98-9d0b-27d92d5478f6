# Prompt Library: Best Practices for Guiding Cline

## Pattern: "Socratic Debugging"
*   **When to use:** When the Agent is stuck on a bug it can't solve.
*   **Bad Prompt:** "Fix the bug."
*   **Good Prompt:**
    > "Let's debug this step-by-step.
    > 1. What was the expected behavior of the function `X`?
    > 2. What was the actual output or error?
    > 3. Trace the values of variables `a`, `b`, and `c` as they enter the function.
    > 4. Based on this trace, where does the logic first deviate from the expectation?"

## Pattern: "Design by Constraints"
*   **When to use:** When asking the Agent to design a new component.
*   **Bad Prompt:** "Design a caching system."
*   **Good Prompt:**
    > "Design a caching system with the following constraints:
    > - **Latency:** p99 must be under 50ms.
    > - **Consistency:** Must be eventually consistent, with a max lag of 5 seconds.
    > - **Technology:** Must use Redis, as per our `decision_log.md` (ADR-002).
    > - **Rule:** Must adhere to rule [ARCH_04] regarding fault tolerance.
    > Please provide two options and their trade-offs."