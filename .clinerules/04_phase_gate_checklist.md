# Phase Gate Checklist (阶段关卡检查清单) v1.0

**目的:** 在每个开发阶段结束时，强制进行一次结构化的自我反思和完整性检查，以确保所有必需的产出物都已完成且质量合格。

**使用流程:** 在我（Cline）认为一个阶段已经完成，并准备向用户报告或请求进入下一阶段**之前**，必须加载并遵循此文件。我必须在我的 `<thinking>` 过程中，以思维链的形式，明确地、逐项地回答清单中的问题。

---

## Phase 0.5 Gate: Request Classification & Documentation Alignment (NEW v3.3.0)

**在进行任何代码实现之前，必须在 `<thinking>` 中回答以下所有问题:**

### 1. 请求分类检查 (Request Classification Check)

*   `[ ]` **(Y/N)** 我是否已正确分类此请求为以下类型之一？
    *   **Type A: 新功能** → 需要Phase 1-2文档后再Phase 3实现
    *   **Type B: Bug修复** → 需要根因分析和影响评估
    *   **Type C: 文档更新** → 允许直接文档修改
    *   **Type D: 配置变更** → 需要配置影响分析

### 2. 文档对齐强制检查 (Mandatory Documentation Alignment)

*   `[ ]` **(Y/N)** 我是否已加载并审查了所有相关的现有文档？
    *   **必检文档:** `resumix_mvp.md`, `docs/api_specification_current.md`, `resume_app/docs/`
*   `[ ]` **(Y/N)** 我是否已识别出当前文档与拟议变更之间的所有差异？
*   `[ ]` **(Y/N)** 我是否已**首先更新文档**以反映拟议的变更？
*   `[ ]` **(Y/N)** 我是否已运行文档对齐检查工具验证一致性？
    *   **工具:** `python3 .clinerules/verification_harness/quick_doc_check.py .`

### 3. 反思性问题 (Reflective Questions)

*   **问题1: (文档优先)** 我是否确保在编写任何代码之前，所有相关文档都已更新以反映拟议的变更？
    *   *我的回答:* ...
*   **问题2: (概念一致性)** 我的文档更新是否与现有系统架构和命名约定保持一致？
    *   *我的回答:* ...

---

## Phase 1 Gate: Formal Specification & Architectural Design -> Logic Generation

**在请求进入 Phase 2 之前，必须在 `<thinking>` 中回答以下所有问题:**

### 1. 产出物清单 (Deliverables Checklist)

*   `[ ]` **(Y/N)** `[project_name].spec.md` 是否已创建并填充了来自 `.intent.yaml` 的 Gherkin 场景？
*   `[ ]` **(Y/N)** `deployment.config.toml` 是否已创建并反映了项目的技术栈和环境约束？
*   `[ ]` **(Y/N)** `high_level_architecture.md` (或类似文档) 是否已创建，并清晰地展示了系统的宏观组件和关系？
*   `[ ]` **(Y/N)** **(关键逻辑识别)** 是否已识别出系统中的关键、复杂或高风险逻辑？
*   `[ ]` **(Y/N)** **(TLA+模型)** 如果识别出关键逻辑，是否已为其创建了 `[project_name].tla` 形式化模型？
*   `[ ]` **(Y/N)** **(接口定义)** 是否已为核心的服务、仓库和模块创建了明确的接口规约文档？
*   `[ ]` **(Y/N) (代码考古 - 仅限重构项目)** 我是否已系统性地审查了旧版代码，并与新规约进行了差异分析？
    *   **简单系统标准 (<50 函数):** 已识别核心业务逻辑、主要错误处理模式、关键数据流
    *   **复杂系统标准 (≥50 函数):** 已完成以下深度分析：
        *   **错误处理模式分析:** 识别并分类所有错误处理模式（建议≥90%覆盖率）
        *   **状态管理复杂性评估:** 文档化所有状态机、状态转换规则和验证逻辑
        *   **并发和事务模式:** 分析锁定策略、事务边界、资源管理模式
        *   **数据验证和边缘案例:** 识别输入验证、边界条件、异常路径处理
        *   **性能关键路径:** 识别性能瓶颈、缓存策略、优化模式
*   `[ ]` **(Y/N) (需求完整性 - 仅限重构项目)** 我是否已将旧代码中所有需要保留的关键功能、边缘案例和隐性规则，都补充到了新的 `.spec.md` 文件中？
    *   **完整性验证标准:**
        *   **功能覆盖:** 所有用户可见功能已在 `.spec.md` 中有对应场景
        *   **错误处理:** 关键错误情况已转化为 Gherkin 异常场景
        *   **边缘案例:** 已识别的边缘案例已作为场景变体记录
        *   **业务规则:** 隐性业务规则已显式化为 Given/When/Then 条件
        *   **数据约束:** 数据验证规则已转化为可测试的断言
*   `[ ]` **(Y/N) (实现模式文档化 - 复杂重构项目)** 对于复杂遗留系统，我是否已创建了详细的实现模式文档？
    *   **必需文档 (如适用):**
        *   `plans/specs/error_handling_strategy.md`: 错误分类、处理策略、恢复机制
        *   `plans/specs/state_management_specification.md`: 状态机规约、转换规则、持久化策略
        *   `plans/specs/data_validation_and_edge_cases.md`: 验证框架、边缘案例处理
        *   `plans/specs/concurrency_and_transaction_specification.md`: 并发控制、事务管理
        *   `plans/specs/function_level_implementation_details.md`: 关键函数实现指南

### 2. 反思性问题 (Reflective Questions)

*   **问题1: (职责分离)** 我设计的架构是否清晰地区分了不同组件的职责？（例如，Player Bot vs Admin Bot, Fast vs Slow Layer）。我是否为这种分离创建了独立的设计文档？
    *   *我的回答:* ...
*   **问题2: (可验证性)** 我的 `spec.md` 文件中的每个 `Scenario` 是否都足够清晰，可以被转化为一个自动化的测试用例？
    *   *我的回答:* ...
*   **问题3: (可追溯性)** 从 `.intent.yaml` 的一个用户故事，到 `.spec.md` 的一个场景，再到架构图中的一个组件，这条线索是否清晰可追溯？
    *   *我的回答:* ...
*   **问题4: (复杂系统分析深度 - 仅限复杂重构项目)** 对于复杂遗留系统，我的分析是否达到了足够的深度，能够支持 Phase 2 的健壮性要求？
    *   **自检标准:**
        *   我是否能够回答"系统中有哪些类型的错误，每种错误应该如何处理"？
        *   我是否理解了所有关键状态转换的前置条件和后置条件？
        *   我是否识别了所有可能的并发冲突点和数据一致性要求？
        *   我是否掌握了系统的性能关键路径和优化策略？
    *   *我的回答:* ...

---

## Phase 2 Gate: Logic Generation & Verification -> Representation & Deployment

**在请求进入 Phase 3 之前，必须在 `<thinking>` 中回答以下所有问题:**

### 1. 产出物清单 (Deliverables Checklist)

*   `[ ]` **(Y/N)** 是否已根据接口规约实现了所有核心的仓库（Repository）和服务（Service）？
*   `[ ]` **(Y/N)** 是否已编写了数据库迁移脚本，并成功执行？
*   `[ ]` **(Y/N)** **(验证检查)** 是否已根据 `.spec.md` 和 `.tla` 创建了自动化的验证检查（如单元测试、集成测试）？
*   `[ ]` **(Y/N)** **(测试覆盖率)** 关键逻辑的测试覆盖率是否达到了可接受的水平？

#### **新增: 功能完整性验证 (v3.2.0)**

*   `[ ]` **(Y/N)** **(代码学习验证)** 是否已深入分析旧代码中相同功能的实现模式、错误处理和边缘情况处理？
*   `[ ]` **(Y/N)** **(服务初始化验证)** 是否已理解并正确实现现有服务的初始化模式和依赖关系？
*   `[ ]` **(Y/N)** **(功能完整性标准)** TODO 占位符是否少于总代码的 20%？核心业务逻辑是否完整实现（非仅框架）？
*   `[ ]` **(Y/N)** **(服务实现验证)** 关键服务是否都有实际实现（非 nil 占位符）？是否能正常初始化和运行？
*   `[ ]` **(Y/N)** **(规格覆盖度验证)** 实现是否覆盖了 Phase 1 规格中的所有核心功能？用户是否能完成主要业务流程？
*   `[ ]` **(Y/N)** **(现实兼容性验证)** 新实现是否基于现有代码模式而非理想化设计？是否已验证与现有系统的集成兼容性？
*   `[ ]` **(Y/N)** **(业务场景测试)** 是否已测试实际业务场景和用户工作流程（非仅编译成功）？

#### **新增: 代码实现交叉验证 (v3.2.0)**

*   `[ ]` **(Y/N)** **(TODO扫描验证)** 是否已系统性扫描所有代码文件中的TODO/FIXME/HACK注释，并验证关键功能无TODO阻塞？
*   `[ ]` **(Y/N)** **(方法实现验证)** 是否已验证所有公开接口方法都有实际实现（非空方法体或panic占位符）？
*   `[ ]` **(Y/N)** **(数据库操作验证)** 是否已验证所有数据库CRUD操作都有实际实现并能正确执行？
*   `[ ]` **(Y/N)** **(API端点验证)** 是否已验证所有API端点都有完整的处理逻辑（非仅返回"not implemented"）？
*   `[ ]` **(Y/N)** **(错误处理实现验证)** 是否已验证关键错误处理路径都有实际实现（非仅log.Error占位符）？
*   `[ ]` **(Y/N)** **(集成测试验证)** 是否已通过实际API调用验证端到端功能流程？

### 2. 强制性验证工具执行 (v3.2.0)

**在回答反思性问题之前，必须执行以下验证工具:**

*   `[ ]` **(Y/N)** 是否已执行代码实现验证工具: `bash .clinerules/verification_harness/code_implementation_validator.sh`
*   `[ ]` **(Y/N)** 是否已执行文档-代码一致性检查: `python3 .clinerules/verification_harness/doc_code_consistency_checker.py .`
*   `[ ]` **(Y/N)** 验证工具是否显示"PASS"或"PARTIAL"状态（"FAIL"状态必须修复后重新验证）？
*   `[ ]` **(Y/N)** 是否已查看并分析验证报告中的所有问题？

### 3. 反思性问题 (Reflective Questions)

*   **问题1: (规约符合度)** 我的代码实现是否严格遵循了 Phase 1 中定义的接口和架构设计？是否存在实现与设计不符的地方？
    *   *我的回答:* ...
*   **问题2: (健壮性)** 我是否处理了所有预期的错误情况？我的代码在面对无效输入或依赖项失败时是否足够健壮？
    *   **健壮性验证标准 (基于 Phase 1 分析):**
        *   **错误处理完整性:** 我的实现是否覆盖了 Phase 1 中识别的所有错误类型和处理策略？
        *   **状态一致性:** 我的状态管理是否遵循了 Phase 1 中定义的状态转换规则和验证逻辑？
        *   **并发安全性:** 我的并发控制是否实现了 Phase 1 中识别的锁定策略和事务边界？
        *   **数据验证:** 我的输入验证是否覆盖了 Phase 1 中识别的所有边缘案例和约束条件？
        *   **性能要求:** 我的实现是否考虑了 Phase 1 中识别的性能关键路径和优化需求？
    *   *我的回答:* ...

#### **新增: 功能完整性反思 (v3.1.0)**

*   **问题3: (代码学习深度)** 我是否充分学习了旧代码中的实现模式？我的新实现是否基于现有的成功模式而非理想化设计？
    *   **代码学习验证标准:**
        *   **模式理解:** 我是否理解了旧代码中相同功能的实现方式、错误处理和边缘情况？
        *   **初始化模式:** 我是否复制了现有服务的初始化和依赖注入模式？
        *   **业务逻辑保持:** 我是否保持了现有业务逻辑的完整性和一致性？
    *   *我的回答:* ...

*   **问题4: (功能完整性)** 我的实现是否提供了实际的业务价值？用户是否能够完成核心业务流程？
    *   **功能完整性验证标准:**
        *   **TODO 比例:** 我的代码中 TODO 占位符是否少于 20%？
        *   **核心逻辑:** 核心业务逻辑是否完整实现，而非仅有架构框架？
        *   **服务可用性:** 关键服务是否实际可用，而非 nil 占位符？
        *   **用户体验:** 用户是否能获得实际功能，而非 "coming soon" 消息？
    *   *我的回答:* ...

*   **问题5: (现实兼容性)** 我的实现是否与现有系统兼容？是否能在实际环境中正常工作？
    *   **现实兼容性验证标准:**
        *   **集成测试:** 我是否验证了与现有系统组件的实际集成？
        *   **业务场景:** 我是否测试了实际的业务场景和用户工作流程？
        *   **系统行为:** 我的实现是否保持了现有系统的行为一致性？
        *   **部署兼容:** 我的代码是否能在现有部署环境中正常运行？
    *   *我的回答:* ...

---

## Phase 2.5 Gate: Functional Completeness & Legacy Integration -> Phase 3 (NEW v3.1.0)

**在请求进入 Phase 3 之前，如果触发了 Phase 2.5，必须在 `<thinking>` 中回答以下所有问题:**

### 1. 触发条件验证 (Trigger Condition Verification)

*   `[ ]` **(Y/N)** Phase 2 实现是否存在 >20% TODO 占位符或核心功能缺失？
*   `[ ]` **(Y/N)** 关键服务是否为 nil 占位符或无法正常初始化？
*   `[ ]` **(Y/N)** 实现是否无法满足 Phase 1 规格中的核心用户场景？

### 2. 代码学习完成度 (Code Learning Completeness)

*   `[ ]` **(Y/N)** 是否已深入分析旧代码中等效功能的实现模式？
*   `[ ]` **(Y/N)** 是否已理解现有错误处理、边缘情况和业务逻辑模式？
*   `[ ]` **(Y/N)** 是否已采用现有的服务初始化和依赖管理模式？

### 3. 功能实现完整度 (Functional Implementation Completeness)

*   `[ ]` **(Y/N)** 核心业务逻辑是否完整实现（非仅架构框架）？
*   `[ ]` **(Y/N)** 关键服务是否有实际实现并能正常工作？
*   `[ ]` **(Y/N)** 用户是否能完成主要业务流程并获得实际价值？

### 4. 现实集成验证 (Reality Integration Verification)

*   `[ ]` **(Y/N)** 新实现是否与现有系统组件成功集成？
*   `[ ]` **(Y/N)** 是否已测试实际业务场景和用户工作流程？
*   `[ ]` **(Y/N)** 是否保持了现有系统的行为一致性和用户体验？

### 5. 反思性问题 (Reflective Questions)

*   **问题1: (学习深度)** 我是否真正理解了旧代码的实现智慧？我的新实现是否体现了这种理解？
    *   *我的回答:* ...
*   **问题2: (价值交付)** 我的实现是否为用户提供了实际的业务价值？还是仅仅满足了技术要求？
    *   *我的回答:* ...
*   **问题3: (系统和谐)** 我的实现是否与现有系统和谐共存？还是创造了不必要的复杂性？
    *   *我的回答:* ...

---

## Phase 3 Gate: Representation & Deployment -> Maintenance

**在完成部署或交付代码之前，必须在 `<thinking>` 中回答以下所有问题:**

### 1. 产出物清单 (Deliverables Checklist)

*   `[ ]` **(Y/N)** 是否已生成所有必要的部署清单（如 `Dockerfile`, `k8s.yaml`）？
*   `[ ]` **(Y/N)** 是否已生成最终的人类可读源代码（如果需要）？
*   `[ ]` **(Y/N)** 是否已更新项目的 `README.md`，包含了如何构建和运行项目的说明？

### 2. 反思性问题 (Reflective Questions)

*   **问题1: (配置正确性)** `deployment.config.toml` 中的配置是否与部署清单中的配置（如环境变量）完全匹配？
    *   *我的回答:* ...
*   **问题2: (可复现性)** 另一个开发者是否可以根据我的文档和脚本，从零开始成功部署和运行这个项目？
    *   *我的回答:* ...
