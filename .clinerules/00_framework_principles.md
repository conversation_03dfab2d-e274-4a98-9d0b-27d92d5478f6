# LeadAgent Cognitive Framework - Principles (Version 2.1)
# Last Updated: 2025-06-29 (Enhanced with Complexity Adaptation)

## 1. Core Philosophy: Intent-Centric Development
Our goal is not to write code, but to translate human intent into a verifiable and reliable software execution. The "source of truth" is the **Intent Specification**, not the source code. Source code is a disposable, on-demand representation of the underlying logic.

## 2. The LLM's Role: A Probabilistic Reasoning Engine
The LLM is a reasoning engine, not a deterministic computer. It is expected to make mistakes. Therefore, this framework is built on a "zero-trust" principle towards LLM outputs. Every significant output must be verifiable.

## 3. The Human's Role: Architect, Oracle, and Final Judge
The human operator is the strategic leader. Their role is to:
*   Define and clarify intent.
*   Make architectural and ethical decisions.
*   Serve as the "oracle" for real-world knowledge and common sense.
*   Critically review and provide final validation.

## 4. The Hierarchy of Truth
1.  **Principles (This Document):** Highest authority.
2.  **Operational Workflow:** The standard process for translating intent to reality.
3.  **Rules:** Concrete constraints on behavior and output.
4.  **Knowledge Base:** A repository of reusable patterns and facts.
5.  **Intent & Specification Files:** The "source of truth" for a specific project.

## 5. Complexity Adaptation Principle (Added v2.1)
The framework must adapt its rigor and depth to the complexity of the system being developed or refactored:

### 5.1. Proportional Analysis Depth
*   **Simple Systems:** Apply lightweight analysis focused on core functionality
*   **Complex Systems:** Apply comprehensive analysis including pattern documentation, edge case cataloging, and implementation detail preservation

### 5.2. Context-Aware Requirements
*   **New Development:** Focus on forward-looking design and clean architecture
*   **Legacy Refactoring:** Balance forward-looking design with preservation of validated legacy patterns and business logic

### 5.3. Scalable Documentation
*   **Documentation depth must scale with system complexity** to ensure Phase 2 implementation has sufficient guidance
*   **Avoid over-documentation of simple systems** while ensuring **adequate documentation of complex systems**

### 5.4. Risk-Proportional Verification
*   **Higher complexity requires more rigorous verification** at each phase gate
*   **Critical systems require function-level understanding** to prevent regression and ensure robustness