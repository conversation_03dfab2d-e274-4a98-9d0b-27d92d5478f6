# Deployment Rules (部署规则) v1.0

## 1. 部署策略原则

### 1.1 部署环境分层
```
Production (生产环境)
    ↑
Staging (预发布环境)  
    ↑
Testing (测试环境)
    ↑
Development (开发环境)
```

### 1.2 部署原则
- **零停机部署**: 生产环境部署不应影响服务可用性
- **可回滚性**: 每次部署都应该能够快速回滚
- **环境一致性**: 所有环境应该使用相同的部署方式
- **自动化优先**: 减少手动操作，提高部署可靠性

## 2. 容器化部署规则

### 2.1 Docker镜像规范
```dockerfile
# 多阶段构建示例
FROM golang:1.21-alpine AS builder
WORKDIR /app
COPY go.mod go.sum ./
RUN go mod download
COPY . .
RUN CGO_ENABLED=0 GOOS=linux go build -o main ./cmd/api

FROM alpine:latest
RUN apk --no-cache add ca-certificates tzdata
WORKDIR /root/
COPY --from=builder /app/main .
EXPOSE 8080
CMD ["./main"]
```

### 2.2 镜像标签策略
```bash
# 标签规范
{registry}/{project}/{service}:{version}

# 示例
registry.example.com/resumix/api:v1.2.3
registry.example.com/resumix/api:latest
registry.example.com/resumix/api:main-abc123f

# 环境特定标签
registry.example.com/resumix/api:staging-v1.2.3
registry.example.com/resumix/api:prod-v1.2.3
```

### 2.3 镜像安全规范
```dockerfile
# 使用非root用户
RUN addgroup -g 1001 -S appgroup && \
    adduser -u 1001 -S appuser -G appgroup
USER appuser

# 最小化镜像
FROM scratch
COPY --from=builder /etc/ssl/certs/ca-certificates.crt /etc/ssl/certs/
COPY --from=builder /app/main /main
EXPOSE 8080
ENTRYPOINT ["/main"]
```

## 3. Kubernetes 部署规则

### 3.1 资源配置规范
```yaml
# deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: resumix-api
  namespace: resumix
spec:
  replicas: 3
  selector:
    matchLabels:
      app: resumix-api
  template:
    metadata:
      labels:
        app: resumix-api
        version: v1.2.3
    spec:
      containers:
      - name: api
        image: registry.example.com/resumix/api:v1.2.3
        ports:
        - containerPort: 8080
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /ready
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5
```

### 3.2 配置管理
```yaml
# configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: resumix-config
  namespace: resumix
data:
  app.yaml: |
    server:
      port: 8080
      host: "0.0.0.0"
    database:
      host: postgres-service
      port: 5432

---
# secret.yaml
apiVersion: v1
kind: Secret
metadata:
  name: resumix-secrets
  namespace: resumix
type: Opaque
data:
  database-password: <base64-encoded-password>
  jwt-secret: <base64-encoded-secret>
```

### 3.3 服务发现配置
```yaml
# service.yaml
apiVersion: v1
kind: Service
metadata:
  name: resumix-api-service
  namespace: resumix
spec:
  selector:
    app: resumix-api
  ports:
  - protocol: TCP
    port: 80
    targetPort: 8080
  type: ClusterIP

---
# ingress.yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: resumix-ingress
  namespace: resumix
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
spec:
  rules:
  - host: api.resumix.example.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: resumix-api-service
            port:
              number: 80
```

## 4. 环境配置管理

### 4.1 配置层次结构
```
configs/
├── base/                 # 基础配置
│   ├── app.yaml
│   └── database.yaml
├── environments/
│   ├── development/      # 开发环境
│   │   ├── app.yaml
│   │   └── secrets.yaml
│   ├── staging/          # 预发布环境
│   │   ├── app.yaml
│   │   └── secrets.yaml
│   └── production/       # 生产环境
│       ├── app.yaml
│       └── secrets.yaml
```

### 4.2 环境变量管理
```bash
# .env.example
# 数据库配置
DB_HOST=localhost
DB_PORT=5432
DB_NAME=resumix
DB_USER=postgres
DB_PASSWORD=your_password

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# 应用配置
APP_ENV=development
APP_PORT=8080
JWT_SECRET=your_jwt_secret

# LLM配置
GEMINI_API_KEY=your_gemini_api_key
GEMINI_MODEL=gemini-pro
```

### 4.3 密钥管理
```bash
# 使用Kubernetes Secrets
kubectl create secret generic resumix-secrets \
  --from-literal=database-password=secret123 \
  --from-literal=jwt-secret=jwt-secret-key \
  --namespace=resumix

# 使用外部密钥管理系统 (如 Vault)
vault kv put secret/resumix/database password=secret123
vault kv put secret/resumix/jwt secret=jwt-secret-key
```

## 5. 部署流水线规则

### 5.1 CI/CD 流水线
```yaml
# .github/workflows/deploy.yml
name: Deploy
on:
  push:
    branches: [main]
    tags: ['v*']

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Run tests
        run: make test

  build:
    needs: test
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Build Docker image
        run: |
          docker build -t ${{ env.IMAGE_NAME }}:${{ github.sha }} .
          docker push ${{ env.IMAGE_NAME }}:${{ github.sha }}

  deploy-staging:
    needs: build
    if: github.ref == 'refs/heads/main'
    runs-on: ubuntu-latest
    steps:
      - name: Deploy to staging
        run: |
          kubectl set image deployment/resumix-api \
            api=${{ env.IMAGE_NAME }}:${{ github.sha }} \
            --namespace=resumix-staging

  deploy-production:
    needs: build
    if: startsWith(github.ref, 'refs/tags/v')
    runs-on: ubuntu-latest
    environment: production
    steps:
      - name: Deploy to production
        run: |
          kubectl set image deployment/resumix-api \
            api=${{ env.IMAGE_NAME }}:${{ github.ref_name }} \
            --namespace=resumix-prod
```

### 5.2 部署验证
```bash
#!/bin/bash
# scripts/verify-deployment.sh

set -e

NAMESPACE=${1:-resumix}
DEPLOYMENT=${2:-resumix-api}

echo "Verifying deployment in namespace: $NAMESPACE"

# 检查部署状态
kubectl rollout status deployment/$DEPLOYMENT -n $NAMESPACE --timeout=300s

# 检查Pod健康状态
kubectl get pods -n $NAMESPACE -l app=$DEPLOYMENT

# 运行健康检查
ENDPOINT=$(kubectl get service $DEPLOYMENT-service -n $NAMESPACE -o jsonpath='{.status.loadBalancer.ingress[0].ip}')
curl -f http://$ENDPOINT/health || exit 1

echo "Deployment verification completed successfully"
```

## 6. 蓝绿部署规则

### 6.1 蓝绿部署策略
```yaml
# blue-green-deployment.yaml
apiVersion: argoproj.io/v1alpha1
kind: Rollout
metadata:
  name: resumix-api
spec:
  replicas: 5
  strategy:
    blueGreen:
      activeService: resumix-api-active
      previewService: resumix-api-preview
      autoPromotionEnabled: false
      scaleDownDelaySeconds: 30
      prePromotionAnalysis:
        templates:
        - templateName: success-rate
        args:
        - name: service-name
          value: resumix-api-preview
  selector:
    matchLabels:
      app: resumix-api
  template:
    metadata:
      labels:
        app: resumix-api
    spec:
      containers:
      - name: api
        image: registry.example.com/resumix/api:v1.2.3
```

### 6.2 金丝雀部署
```yaml
# canary-deployment.yaml
apiVersion: argoproj.io/v1alpha1
kind: Rollout
metadata:
  name: resumix-api
spec:
  replicas: 10
  strategy:
    canary:
      steps:
      - setWeight: 10
      - pause: {duration: 1m}
      - setWeight: 20
      - pause: {duration: 1m}
      - setWeight: 50
      - pause: {duration: 1m}
      - setWeight: 100
      canaryService: resumix-api-canary
      stableService: resumix-api-stable
```

## 7. 监控和告警

### 7.1 部署监控
```yaml
# monitoring.yaml
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: resumix-api
spec:
  selector:
    matchLabels:
      app: resumix-api
  endpoints:
  - port: metrics
    interval: 30s
    path: /metrics
```

### 7.2 告警规则
```yaml
# alerts.yaml
groups:
- name: resumix-api
  rules:
  - alert: HighErrorRate
    expr: rate(http_requests_total{status=~"5.."}[5m]) > 0.1
    for: 5m
    labels:
      severity: critical
    annotations:
      summary: "High error rate detected"
      
  - alert: PodCrashLooping
    expr: rate(kube_pod_container_status_restarts_total[15m]) > 0
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "Pod is crash looping"
```

## 8. 备份和恢复

### 8.1 数据备份策略
```bash
#!/bin/bash
# scripts/backup-database.sh

BACKUP_DIR="/backups/$(date +%Y%m%d)"
mkdir -p $BACKUP_DIR

# 数据库备份
kubectl exec -n resumix postgres-0 -- pg_dump -U postgres resumix > $BACKUP_DIR/database.sql

# 配置备份
kubectl get configmap -n resumix -o yaml > $BACKUP_DIR/configmaps.yaml
kubectl get secret -n resumix -o yaml > $BACKUP_DIR/secrets.yaml

# 上传到对象存储
aws s3 cp $BACKUP_DIR s3://resumix-backups/$(date +%Y%m%d)/ --recursive
```

### 8.2 灾难恢复计划
```bash
#!/bin/bash
# scripts/disaster-recovery.sh

BACKUP_DATE=${1:-$(date +%Y%m%d)}
BACKUP_DIR="/tmp/restore-$BACKUP_DATE"

# 下载备份
aws s3 cp s3://resumix-backups/$BACKUP_DATE/ $BACKUP_DIR --recursive

# 恢复数据库
kubectl exec -n resumix postgres-0 -- psql -U postgres -c "DROP DATABASE IF EXISTS resumix;"
kubectl exec -n resumix postgres-0 -- psql -U postgres -c "CREATE DATABASE resumix;"
kubectl exec -i -n resumix postgres-0 -- psql -U postgres resumix < $BACKUP_DIR/database.sql

# 恢复配置
kubectl apply -f $BACKUP_DIR/configmaps.yaml
kubectl apply -f $BACKUP_DIR/secrets.yaml

# 重启服务
kubectl rollout restart deployment/resumix-api -n resumix
```

## 9. 安全部署规则

### 9.1 网络安全
```yaml
# network-policy.yaml
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: resumix-api-netpol
  namespace: resumix
spec:
  podSelector:
    matchLabels:
      app: resumix-api
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          name: ingress-nginx
    ports:
    - protocol: TCP
      port: 8080
  egress:
  - to:
    - namespaceSelector:
        matchLabels:
          name: database
    ports:
    - protocol: TCP
      port: 5432
```

### 9.2 Pod安全策略
```yaml
# pod-security-policy.yaml
apiVersion: policy/v1beta1
kind: PodSecurityPolicy
metadata:
  name: resumix-psp
spec:
  privileged: false
  allowPrivilegeEscalation: false
  requiredDropCapabilities:
    - ALL
  volumes:
    - 'configMap'
    - 'emptyDir'
    - 'projected'
    - 'secret'
    - 'downwardAPI'
    - 'persistentVolumeClaim'
  runAsUser:
    rule: 'MustRunAsNonRoot'
  seLinux:
    rule: 'RunAsAny'
  fsGroup:
    rule: 'RunAsAny'
```

## 10. 性能优化

### 10.1 资源配置优化
```yaml
# 根据实际负载调整资源配置
resources:
  requests:
    memory: "256Mi"    # 基于实际内存使用
    cpu: "200m"        # 基于实际CPU使用
  limits:
    memory: "1Gi"      # 防止内存泄漏
    cpu: "1000m"       # 防止CPU过载
```

### 10.2 水平扩缩容
```yaml
# hpa.yaml
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: resumix-api-hpa
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: resumix-api
  minReplicas: 3
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
```

## 11. 违规检查清单

### 11.1 部署前检查
- [ ] 所有测试通过
- [ ] 代码已经过代码审查
- [ ] 配置文件已更新
- [ ] 数据库迁移已准备
- [ ] 监控和告警已配置

### 11.2 部署过程检查
- [ ] 使用正确的镜像标签
- [ ] 资源配置合理
- [ ] 健康检查配置正确
- [ ] 网络策略已应用
- [ ] 备份已完成

### 11.3 部署后检查
- [ ] 服务健康检查通过
- [ ] 监控指标正常
- [ ] 日志输出正常
- [ ] 性能指标符合预期
- [ ] 回滚计划已准备

## 12. 生产部署配置规范 (新增 2025-07-04)

### 12.1 环境配置管理
```bash
# 生产环境配置模板结构
deployment/
├── production.env.example      # 生产环境变量模板
├── deploy.sh                  # 自动化部署脚本
├── database/
│   └── setup_production_db.sh # 数据库初始化脚本
├── monitoring/
│   ├── prometheus.yml         # Prometheus 配置
│   └── alert_rules.yml        # 告警规则
└── nginx/
    └── resumix.conf           # Nginx 反向代理配置
```

### 12.2 安全配置要求
```bash
# 必需的安全配置
- SSL/TLS 证书配置和 HTTPS 强制
- 安全密钥生成 (JWT, 加密密钥, CSRF 密钥)
- 数据库连接加密 (SSL required)
- Redis 认证和连接加密
- 防火墙规则和端口限制
- 安全头配置 (HSTS, CSP, X-Frame-Options)
```

### 12.3 监控和告警配置
```yaml
# 关键监控指标
application_metrics:
  - request_rate: 请求速率监控
  - error_rate: 错误率告警 (>5%)
  - latency: 延迟监控 (95th percentile)
  - security_violations: 安全违规事件

system_metrics:
  - cpu_usage: CPU 使用率 (>80%)
  - memory_usage: 内存使用率 (>85%)
  - disk_space: 磁盘空间 (>90%)
  - network_traffic: 网络流量监控

database_metrics:
  - connection_count: 数据库连接数
  - query_performance: 查询性能监控
  - slow_queries: 慢查询告警
```

### 12.4 部署脚本规范
```bash
# 部署脚本必需功能
- 干运行模式 (--dry-run)
- 环境验证和前置条件检查
- 自动化测试执行 (可选 --skip-tests)
- 备份创建 (可选 --skip-backup)
- 服务健康检查和验证
- 回滚支持和故障恢复
```

### 12.5 数据库部署配置
```sql
-- 生产数据库配置要求
- 连接池优化: max_connections=25, idle=5
- SSL 连接强制: sslmode=require
- 性能调优: shared_buffers, work_mem 配置
- 备份策略: 每日自动备份，30天保留
- 监控配置: 连接数、查询性能、慢查询
```

### 12.6 Nginx 反向代理配置
```nginx
# 生产 Nginx 配置要求
- HTTPS 强制和 HTTP 重定向
- 安全头配置 (HSTS, CSP, X-Frame-Options)
- 速率限制: API 10r/s, 上传 2r/s
- 连接限制: 每 IP 20 连接
- Gzip 压缩和缓存配置
- 健康检查和指标端点配置
```

---

**注意**: 这些规则应该根据具体项目需求进行调整和补充。部署安全性和可靠性是首要考虑因素。生产部署配置必须经过安全审查和性能测试验证。
