# Testing Development Rules (测试开发规则) v1.0

## 1. 测试策略原则

### 1.1 测试金字塔
```
    /\     E2E Tests (少量)
   /  \    Integration Tests (适量)  
  /____\   Unit Tests (大量)
```

- **单元测试**: 70% - 测试单个函数/方法
- **集成测试**: 20% - 测试组件间交互
- **端到端测试**: 10% - 测试完整用户流程

### 1.2 测试原则
- **快速反馈**: 单元测试应在秒级完成
- **独立性**: 测试之间不应相互依赖
- **可重复**: 测试结果应该是确定性的
- **可维护**: 测试代码应该易于理解和维护

## 2. Go 单元测试规则

### 2.1 测试文件组织
```go
// 文件结构
package_name/
├── service.go
├── service_test.go      // 单元测试
├── service_integration_test.go  // 集成测试
└── testdata/           // 测试数据文件
    └── sample.json
```

### 2.2 测试命名规范
```go
// 测试函数命名: Test + 被测试函数名 + 测试场景
func TestCreateUser_ValidInput_ReturnsUser(t *testing.T) {}
func TestCreateUser_InvalidEmail_ReturnsError(t *testing.T) {}
func TestCreateUser_DuplicateEmail_ReturnsError(t *testing.T) {}

// 基准测试命名
func BenchmarkCreateUser(b *testing.B) {}

// 示例测试命名
func ExampleCreateUser() {}
```

### 2.3 测试结构模式
```go
func TestCreateUser_ValidInput_ReturnsUser(t *testing.T) {
    // Arrange (准备)
    userRepo := &MockUserRepository{}
    userService := NewUserService(userRepo)
    input := &CreateUserInput{
        Email: "<EMAIL>",
        Name:  "Test User",
    }
    
    // Act (执行)
    user, err := userService.CreateUser(context.Background(), input)
    
    // Assert (断言)
    assert.NoError(t, err)
    assert.NotNil(t, user)
    assert.Equal(t, input.Email, user.Email)
    assert.Equal(t, input.Name, user.Name)
}
```

### 2.4 表驱动测试
```go
func TestValidateEmail(t *testing.T) {
    tests := []struct {
        name    string
        email   string
        wantErr bool
    }{
        {"valid email", "<EMAIL>", false},
        {"invalid email", "invalid-email", true},
        {"empty email", "", true},
        {"email without domain", "test@", true},
    }
    
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            err := ValidateEmail(tt.email)
            if (err != nil) != tt.wantErr {
                t.Errorf("ValidateEmail() error = %v, wantErr %v", err, tt.wantErr)
            }
        })
    }
}
```

## 3. Mock 和 Stub 规则

### 3.1 接口设计
```go
// 为了便于测试，使用接口定义依赖
type UserRepository interface {
    Create(ctx context.Context, user *User) error
    GetByEmail(ctx context.Context, email string) (*User, error)
}

type UserService struct {
    repo UserRepository  // 依赖接口而非具体实现
}
```

### 3.2 Mock 实现
```go
// 手动Mock实现
type MockUserRepository struct {
    users map[string]*User
    err   error
}

func (m *MockUserRepository) Create(ctx context.Context, user *User) error {
    if m.err != nil {
        return m.err
    }
    m.users[user.Email] = user
    return nil
}

func (m *MockUserRepository) SetError(err error) {
    m.err = err
}

// 使用testify/mock
type MockUserRepository struct {
    mock.Mock
}

func (m *MockUserRepository) Create(ctx context.Context, user *User) error {
    args := m.Called(ctx, user)
    return args.Error(0)
}
```

### 3.3 依赖注入测试
```go
func TestUserService_CreateUser(t *testing.T) {
    // 注入Mock依赖
    mockRepo := &MockUserRepository{}
    service := NewUserService(mockRepo)
    
    // 设置Mock期望
    mockRepo.On("Create", mock.Anything, mock.Anything).Return(nil)
    
    // 执行测试
    err := service.CreateUser(context.Background(), &User{})
    
    // 验证Mock调用
    assert.NoError(t, err)
    mockRepo.AssertExpectations(t)
}
```

## 4. 集成测试规则

### 4.1 数据库集成测试
```go
func TestUserRepository_Integration(t *testing.T) {
    // 使用测试数据库
    db := setupTestDB(t)
    defer cleanupTestDB(t, db)
    
    repo := NewUserRepository(db)
    
    // 测试数据库操作
    user := &User{Email: "<EMAIL>"}
    err := repo.Create(context.Background(), user)
    
    assert.NoError(t, err)
    assert.NotEmpty(t, user.ID)
}

func setupTestDB(t *testing.T) *sql.DB {
    // 连接测试数据库
    db, err := sql.Open("postgres", "postgres://test:test@localhost/test_db")
    require.NoError(t, err)
    
    // 运行迁移
    runMigrations(t, db)
    
    return db
}
```

### 4.2 HTTP API 集成测试
```go
func TestCreateUserAPI_Integration(t *testing.T) {
    // 启动测试服务器
    server := setupTestServer(t)
    defer server.Close()

    // 准备请求
    payload := `{"email":"<EMAIL>","name":"Test User"}`
    req, _ := http.NewRequest("POST", server.URL+"/users", strings.NewReader(payload))
    req.Header.Set("Content-Type", "application/json")

    // 发送请求
    resp, err := http.DefaultClient.Do(req)
    require.NoError(t, err)
    defer resp.Body.Close()

    // 验证响应
    assert.Equal(t, http.StatusCreated, resp.StatusCode)

    var user User
    err = json.NewDecoder(resp.Body).Decode(&user)
    require.NoError(t, err)
    assert.Equal(t, "<EMAIL>", user.Email)
}
```

### 4.3 LLM集成测试
```go
func TestLLMIntegration_ResumeProcessing(t *testing.T) {
    // 跳过没有API密钥的测试
    apiKey := os.Getenv("LLM_API_KEY")
    if apiKey == "" {
        t.Skip("LLM_API_KEY not set, skipping LLM integration test")
    }

    // 准备测试简历文件
    resumeContent := `
    姓名：张三
    职位：软件工程师
    经验：3年
    技能：Go, Python, Docker
    `

    // 创建临时文件
    tmpFile, err := os.CreateTemp("", "test_resume_*.txt")
    require.NoError(t, err)
    defer os.Remove(tmpFile.Name())

    _, err = tmpFile.WriteString(resumeContent)
    require.NoError(t, err)
    tmpFile.Close()

    // 测试LLM处理
    llmService := NewLLMService(apiKey)
    result, err := llmService.ProcessResume(context.Background(), tmpFile.Name())

    // 验证LLM响应
    assert.NoError(t, err)
    assert.NotEmpty(t, result.Report)
    assert.Contains(t, []string{"PROCEED", "FLAG", "REJECT"}, result.Verdict)
    assert.GreaterOrEqual(t, result.RiskIndex, 0)
    assert.LessOrEqual(t, result.RiskIndex, 10)

    // 验证响应包含关键信息
    assert.Contains(t, result.Report, "张三")
    assert.Contains(t, result.Report, "软件工程师")
}
```

### 4.4 数据库兼容性测试
```go
func TestDatabaseCompatibility_MultipleDrivers(t *testing.T) {
    testCases := []struct {
        name       string
        driverName string
        connString string
    }{
        {
            name:       "PostgreSQL",
            driverName: "postgres",
            connString: "postgres://test:test@localhost/test_db?sslmode=disable",
        },
        {
            name:       "SQLite",
            driverName: "postgres",
            connString: "postgresql://test:test@localhost:5432/test_db?sslmode=disable",
        },
    }

    for _, tc := range testCases {
        t.Run(tc.name, func(t *testing.T) {
            // 设置数据库连接
            db, err := setupDatabaseForTest(tc.driverName, tc.connString)
            require.NoError(t, err)
            defer db.Close()

            // 测试基本CRUD操作
            repo := NewAdjudicationRepository(db)

            // 创建测试任务
            task := &AdjudicationLead{
                ID:          uuid.New().String(),
                JobTitle:    "测试职位",
                Status:      "PENDING",
                CreatedAt:   time.Now(),
            }

            err = repo.Create(context.Background(), task)
            assert.NoError(t, err)

            // 测试更新操作（验证参数占位符兼容性）
            err = repo.UpdateResult(context.Background(), task.ID,
                stringPtr("测试报告"), stringPtr("PROCEED"), intPtr(2))
            assert.NoError(t, err)

            // 验证更新结果
            updated, err := repo.GetByID(context.Background(), task.ID)
            assert.NoError(t, err)
            assert.Equal(t, "COMPLETED", updated.Status)
            assert.Equal(t, "测试报告", *updated.ReportMarkdown)
            assert.Equal(t, "PROCEED", *updated.Verdict)
            assert.Equal(t, 2, *updated.RiskIndex)
        })
    }
}
```

### 4.5 Worker系统集成测试
```go
func TestWorkerIntegration_EndToEndProcessing(t *testing.T) {
    // 设置完整的测试环境
    ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
    defer cancel()

    // 启动Redis (用于任务队列)
    redisClient := setupTestRedis(t)
    defer redisClient.Close()

    // 启动数据库
    db := setupTestDatabase(t)
    defer db.Close()

    // 启动Worker
    worker := NewWorker(db, redisClient)
    go worker.Start(ctx)

    // 创建测试任务
    task := &AdjudicationLead{
        ID:       uuid.New().String(),
        JobTitle: "软件工程师",
        Status:   "PENDING",
    }

    // 将任务添加到队列
    err := addTaskToQueue(redisClient, task)
    require.NoError(t, err)

    // 等待Worker处理任务
    time.Sleep(10 * time.Second)

    // 验证任务处理结果
    repo := NewAdjudicationRepository(db)
    processed, err := repo.GetByID(ctx, task.ID)
    require.NoError(t, err)

    // 验证任务状态和结果
    assert.Equal(t, "COMPLETED", processed.Status)
    assert.NotNil(t, processed.ReportMarkdown)
    assert.NotNil(t, processed.Verdict)
    assert.NotNil(t, processed.RiskIndex)
    assert.NotEmpty(t, *processed.ReportMarkdown)
}
```

## 5. 端到端测试规则

### 5.1 测试环境准备
```go
func TestE2E_UserRegistrationFlow(t *testing.T) {
    if testing.Short() {
        t.Skip("Skipping E2E test in short mode")
    }
    
    // 启动完整的测试环境
    env := setupE2EEnvironment(t)
    defer env.Cleanup()
    
    // 执行完整的用户注册流程
    // 1. 注册用户
    // 2. 验证邮箱
    // 3. 登录
    // 4. 访问受保护资源
}
```

### 5.2 前端E2E测试 (使用Playwright)
```javascript
// tests/e2e/user-registration.spec.ts
import { test, expect } from '@playwright/test';

test('user registration flow', async ({ page }) => {
  // 访问注册页面
  await page.goto('/register');
  
  // 填写注册表单
  await page.fill('[data-testid=email]', '<EMAIL>');
  await page.fill('[data-testid=password]', 'password123');
  await page.click('[data-testid=submit]');
  
  // 验证注册成功
  await expect(page.locator('[data-testid=success-message]')).toBeVisible();
});
```

## 6. 测试数据管理

### 6.1 测试数据策略
```go
// 使用工厂模式创建测试数据
func NewTestUser() *User {
    return &User{
        ID:    uuid.New(),
        Email: fmt.Sprintf("<EMAIL>", time.Now().UnixNano()),
        Name:  "Test User",
    }
}

// 使用Builder模式定制测试数据
type UserBuilder struct {
    user *User
}

func NewUserBuilder() *UserBuilder {
    return &UserBuilder{user: NewTestUser()}
}

func (b *UserBuilder) WithEmail(email string) *UserBuilder {
    b.user.Email = email
    return b
}

func (b *UserBuilder) Build() *User {
    return b.user
}
```

### 6.2 测试数据清理
```go
func TestWithCleanup(t *testing.T) {
    // 创建测试数据
    user := createTestUser(t)
    
    // 注册清理函数
    t.Cleanup(func() {
        deleteTestUser(t, user.ID)
    })
    
    // 执行测试逻辑
}
```

## 7. 性能测试规则

### 7.1 基准测试
```go
func BenchmarkCreateUser(b *testing.B) {
    service := setupUserService()
    
    b.ResetTimer()
    for i := 0; i < b.N; i++ {
        user := &User{Email: fmt.Sprintf("<EMAIL>", i)}
        service.CreateUser(context.Background(), user)
    }
}

func BenchmarkCreateUserParallel(b *testing.B) {
    service := setupUserService()
    
    b.RunParallel(func(pb *testing.PB) {
        i := 0
        for pb.Next() {
            user := &User{Email: fmt.Sprintf("<EMAIL>", i)}
            service.CreateUser(context.Background(), user)
            i++
        }
    })
}
```

### 7.2 负载测试
```go
func TestCreateUser_LoadTest(t *testing.T) {
    if testing.Short() {
        t.Skip("Skipping load test in short mode")
    }
    
    const (
        concurrency = 100
        requests    = 1000
    )
    
    var wg sync.WaitGroup
    errors := make(chan error, requests)
    
    for i := 0; i < concurrency; i++ {
        wg.Add(1)
        go func() {
            defer wg.Done()
            for j := 0; j < requests/concurrency; j++ {
                if err := createUser(); err != nil {
                    errors <- err
                }
            }
        }()
    }
    
    wg.Wait()
    close(errors)
    
    errorCount := 0
    for err := range errors {
        t.Logf("Error: %v", err)
        errorCount++
    }
    
    if errorCount > requests*0.01 { // 允许1%的错误率
        t.Fatalf("Too many errors: %d/%d", errorCount, requests)
    }
}
```

## 8. 测试覆盖率规则

### 8.1 覆盖率目标
- **单元测试覆盖率**: ≥ 80%
- **集成测试覆盖率**: ≥ 60%
- **关键路径覆盖率**: 100%

### 8.2 覆盖率检查
```bash
# 生成覆盖率报告
go test -coverprofile=coverage.out ./...
go tool cover -html=coverage.out -o coverage.html

# 检查覆盖率阈值
go tool cover -func=coverage.out | grep total | awk '{print $3}' | sed 's/%//' | awk '{if($1<80) exit 1}'
```

### 8.3 覆盖率排除
```go
// 使用build tags排除测试代码
//go:build !test

// 排除不需要测试的代码
func debugFunction() {
    // 这个函数不需要测试覆盖
}
```

## 9. 测试环境配置

### 9.1 测试配置
```go
// config/test.go
func NewTestConfig() *Config {
    return &Config{
        Database: DatabaseConfig{
            Host:     "localhost",
            Port:     5433, // 使用不同的端口
            Database: "test_db",
            User:     "test_user",
            Password: "test_password",
        },
        Redis: RedisConfig{
            Addr: "localhost:6380", // 使用不同的端口
            DB:   1,                 // 使用不同的数据库
        },
    }
}
```

### 9.2 Docker测试环境
```yaml
# docker-compose.test.yml
version: '3.8'
services:
  postgres-test:
    image: postgres:15
    environment:
      POSTGRES_DB: test_db
      POSTGRES_USER: test_user
      POSTGRES_PASSWORD: test_password
    ports:
      - "5433:5432"
    
  redis-test:
    image: redis:7
    ports:
      - "6380:6379"
```

## 10. 持续集成测试

### 10.1 CI/CD 测试流水线
```yaml
# .github/workflows/test.yml
name: Tests
on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
    
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-go@v3
        with:
          go-version: '1.21'
      
      - name: Run tests
        run: |
          go test -v -race -coverprofile=coverage.out ./...
          go tool cover -func=coverage.out
```

### 10.2 测试报告
```bash
# 生成测试报告
go test -v -json ./... | tee test-results.json
go-junit-report < test-results.json > test-results.xml
```

## 11. 测试最佳实践

### 11.1 测试代码质量
- **可读性**: 测试代码应该像文档一样易读
- **简洁性**: 每个测试只验证一个行为
- **独立性**: 测试之间不应有依赖关系
- **确定性**: 测试结果应该是可预测的

### 11.2 测试维护
- **重构友好**: 测试应该支持代码重构
- **失败信息**: 提供清晰的失败信息
- **测试数据**: 使用有意义的测试数据
- **定期清理**: 删除过时和重复的测试

## 12. 违规检查清单

### 12.1 单元测试检查
- [ ] 测试覆盖率达到80%以上
- [ ] 测试命名清晰描述测试场景
- [ ] 使用AAA模式组织测试代码
- [ ] Mock了所有外部依赖
- [ ] 测试执行时间在合理范围内

### 12.2 集成测试检查
- [ ] 使用独立的测试数据库
- [ ] 测试后正确清理数据
- [ ] 覆盖了主要的集成点
- [ ] 测试环境与生产环境一致

### 12.3 E2E测试检查
- [ ] 覆盖了关键用户流程
- [ ] 使用稳定的测试环境
- [ ] 测试数据独立且可重复
- [ ] 失败时提供足够的调试信息

## 13. 性能基准测试规范 (新增 2025-07-04)

### 13.1 性能测试策略
```go
// 性能基准测试结构
func BenchmarkHTTPMiddleware(b *testing.B) {
    // 1. 设置基准测试环境
    // 2. 预热系统
    // 3. 执行基准测试
    // 4. 收集性能指标
}
```

### 13.2 HTTP 中间件性能测试
- **[PERF_HTTP_01]** 中间件性能测试必须测量延迟影响和吞吐量变化
- **[PERF_HTTP_02]** 速率限制中间件性能影响必须量化（如：27倍性能影响）
- **[PERF_HTTP_03]** 中间件排序对性能的影响必须测试和优化
- **[PERF_HTTP_04]** 内存分配监控必须包含在性能测试中

### 13.3 缓存性能测试
- **[PERF_CACHE_01]** Redis 缓存性能必须达到 30,000+ ops/sec
- **[PERF_CACHE_02]** 缓存命中率必须保持在 80% 以上
- **[PERF_CACHE_03]** 并发操作测试必须验证缓存一致性
- **[PERF_CACHE_04]** 缓存性能测试必须使用独立的 Redis 实例

### 13.4 数据库性能测试
- **[PERF_DB_01]** 数据库性能测试需要完整的 PostgreSQL 配置
- **[PERF_DB_02]** 连接池设置必须优化（推荐：25 最大连接，5 空闲）
- **[PERF_DB_03]** 查询性能监控必须包含慢查询识别
- **[PERF_DB_04]** 数据库性能测试必须使用独立的测试数据库

### 13.5 并发性能测试
```go
// 并发性能测试示例
func TestPerformanceUnderConcurrentLoad(t *testing.T) {
    const numGoroutines = 50
    const requestsPerGoroutine = 20

    // 并发测试实现
    var wg sync.WaitGroup
    for i := 0; i < numGoroutines; i++ {
        wg.Add(1)
        go func() {
            defer wg.Done()
            for j := 0; j < requestsPerGoroutine; j++ {
                // 执行并发请求
            }
        }()
    }
    wg.Wait()
}
```

### 13.6 性能基准测试报告要求
- **[PERF_REPORT_01]** 性能测试必须生成详细的基准测试报告
- **[PERF_REPORT_02]** 报告必须包含：吞吐量、延迟、内存使用、CPU 使用率
- **[PERF_REPORT_03]** 性能回归必须阻止部署流程
- **[PERF_REPORT_04]** 性能优化建议必须包含具体的改进措施

### 13.7 性能测试环境要求
- **[PERF_ENV_01]** 基准测试隔离需要独立的测试数据库和 Redis 实例
- **[PERF_ENV_02]** 性能测试环境必须与生产环境配置一致
- **[PERF_ENV_03]** 性能测试必须在稳定的硬件环境中执行
- **[PERF_ENV_04]** 性能基准必须建立并定期更新

### 13.8 性能优化洞察
- **中间件排序优化**: 将昂贵的中间件放在请求处理链的最后
- **缓存策略优化**: 命中率超过 80% 表明有效的缓存策略
- **内存管理**: 每操作内存分配应监控垃圾回收影响
- **并发处理**: 系统在适当的中间件排序下处理并发请求良好

### 13.9 性能测试检查清单
- [ ] HTTP 中间件性能影响已量化
- [ ] 缓存性能达到目标指标 (30k+ ops/sec, 80%+ 命中率)
- [ ] 数据库连接池配置已优化
- [ ] 并发负载测试通过
- [ ] 性能基准测试报告已生成
- [ ] 性能回归检查通过
- [ ] 内存泄漏检查通过
- [ ] CPU 使用率在合理范围内
