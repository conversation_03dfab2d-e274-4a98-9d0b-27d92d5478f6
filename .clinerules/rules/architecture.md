# Architecture Rules

*   **[ARCH_01 | Intent-Behavior Separation]:** System logic MUST be separated from its external representation (e.g., API handlers, UI components). Handlers parse intent, call the core logic, and format the result. They do not contain business logic. (Refactored from A001, A002)
*   **[ARCH_02 | Configuration Externalization]:** All configuration values (ports, external URLs, keys) MUST NOT be hardcoded. They must be defined in environment variables or configuration files/database. (Refactored from A005)
*   **[ARCH_03 | LLM Interaction Robustness]:** Interactions with external LLMs MUST follow the "Simple Text In, Robust Parsing Out" pattern. The system's Go/TS code is responsible for parsing simple, delimited text from the LLM, not for handling fragile, complex JSON. (Elevating A015 to a core architectural principle).
*   **[ARCH_04 | ORM Usage]:** Database interactions in Go MUST primarily use the established ORM ('gorm'). Direct SQL queries require justification and review. (From A003)
*   **[ARCH_05 | Dependency Management]:** AVOID introducing new third-party dependencies without prior discussion and recording the decision. Check 'go.mod' and 'package.json'. (From A004)
*   **[ARCH_06 | File Conciseness]:** Files MUST be kept concise and focused. Vue components > ~150 lines or Go files containing many handlers/functions SHOULD trigger refactoring/splitting. (From A006)
*   **[ARCH_07 | Robust File Paths]:** Configuration file paths MUST be constructed using 'os.Executable()' and 'filepath' package in Go to ensure robustness against working directory changes. (From A007)
*   **[ARCH_08 | Authenticated API Client]:** Ensure all authenticated API requests use the Axios instance configured with interceptors (`apiClient`), not the global `axios`. (From A008)
*   **[ARCH_09 | Global Style Scoping]:** AVOID applying global Flexbox/Grid styles on top-level elements like `body` or `#app` that could conflict with CSS frameworks (e.g., Bootstrap). (From A009)
*   **[ARCH_10 | Flexible Attribute Storage]:** For diverse, evolving LLM-extracted attributes, prefer a dedicated Key-Value attribute table over adding numerous fixed columns to the main model table. (From A010)
*   **[ARCH_11 | Sync vs. Async Tasks]:** User-initiated, immediate-feedback tasks should be synchronous, while background processing tasks should be asynchronous. (From A011)
*   **[ARCH_12 | Configurable Thresholds]:** Vector similarity thresholds MUST be managed via the configuration system, not hardcoded. (From A012)
*   **[ARCH_13 | Event Bus Communication]:** Implement inter-module communication via an event bus (e.g., Redis Streams) to reduce coupling and enhance system scalability and flexibility. (From A013)
*   **[ARCH_14 | Unified Event Structure]:** Define a standard event structure (e.g., EventMeta) to ensure consistency and parsability of event data. (From A014)
*   **[ARCH_15 | Development Process Principle]:** Follow the development process principle: Plan first, then design the framework (considering file size limits and sub-file splitting), and finally fill in the code. (From DS006)

# API Design Rules

*   **[API_01 | Standard Verbs]:** Use standard HTTP verbs correctly (GET, POST, PUT, DELETE, PATCH). (From API001)
*   **[API_02 | Plural Nouns for Resources]:** Resource URLs MUST use plural nouns (e.g., `/users`). (From API002)
*   **[API_03 | Standard Status Codes]:** Use standard HTTP status codes correctly. (From API003)
*   **[API_04 | Standard Error Format]:** Error responses MUST follow the standard JSON format: '{ "error": { "code": "...", "message": "..." } }'. (From API004)

# Worker Architecture Rules

*   **[WORKER_01 | Error Classification Pattern]:** Worker services MUST implement proper error classification to distinguish between:
    - **System Errors**: Database failures, network timeouts, configuration errors (require logging and alerting)
    - **Business Conditions**: Empty queues, resource unavailability, task completion (normal states, no error logging)
    - **Recoverable Errors**: Temporary failures that can be retried (require backoff strategies)

*   **[WORKER_02 | Empty Queue Handling]:** Empty task queues MUST be treated as normal business conditions. Workers should:
    - Poll queues at regular intervals without error logging when empty
    - Use appropriate sleep/backoff mechanisms to avoid resource waste
    - Distinguish between "no tasks available" and "queue access failure"

*   **[WORKER_03 | Type-Safe Error Detection]:** Use Go's type-safe error handling patterns:
    ```go
    // Preferred: Type-safe error detection
    var noTaskErr *services.NoTaskAvailableError
    if errors.As(err, &noTaskErr) {
        // Handle as normal business condition
        return nil
    }

    // Avoid: String-based error detection
    if err.Error() == "redis: nil" {
        // Fragile and error-prone
    }
    ```

*   **[WORKER_04 | Graceful Degradation]:** Workers MUST implement graceful degradation patterns:
    - Continue operation when non-critical services are unavailable
    - Provide fallback mechanisms for external service failures
    - Maintain service availability during partial system failures

*   **[WORKER_05 | Resource Management]:** Worker services MUST implement proper resource management:
    - Connection pooling for database and external services
    - Proper cleanup of resources on shutdown
    - Memory and CPU usage monitoring and limits

# Modularity & Responsibility Separation Rules

*   **[MOD_01 | Clear Interface Definitions]:** Define interfaces (e.g., PlayerBotHandler, SessionRouter) to clarify module responsibilities, facilitating decoupling and flexible extension. (From MOD001)
*   **[MOD_02 | Single Responsibility Principle (SRP)]:** Each class or module should focus on a single responsibility to reduce complexity and maintenance difficulties. (From MOD002)
