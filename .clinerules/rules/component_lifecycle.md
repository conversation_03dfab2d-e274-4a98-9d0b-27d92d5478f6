# 组件生命周期管理规则
# Version: 1.0.0
# Created: 2025-07-09
# Purpose: 确保组件生命周期正确管理，防止资源泄漏和事件丢失

## 问题根因分析

### 发现的问题
1. **初始化缺失**: Home.vue挂载时未初始化SSE连接
2. **事件监听缺失**: ReportViewer的@close事件未被父组件监听
3. **资源清理不完整**: 组件卸载时未正确清理资源

### 根本原因
- **[LC_01]** 缺乏组件生命周期检查清单
- **[LC_02]** 父子组件通信规范不明确
- **[LC_03]** 资源管理责任边界模糊
- **[LC_04]** 缺乏自动化的生命周期验证

## 强制性规则

### [LIFECYCLE_01] 组件初始化完整性
**规则**: 每个组件必须在onMounted中完成所有必要的初始化

```typescript
// ✅ 正确的初始化模式
onMounted(() => {
  // 1. 加载配置
  configStore.loadConfig()
  
  // 2. 初始化外部连接
  if (needsSSE) {
    sseService.connect()
  }
  
  // 3. 注册事件监听
  window.addEventListener('custom-event', handler)
  
  // 4. 处理URL参数/路由状态
  handleRouteParams()
  
  // 5. 初始化数据
  loadInitialData()
})
```

**检查清单**:
- [ ] 所有外部服务连接已初始化
- [ ] 所有事件监听器已注册
- [ ] 路由参数已处理
- [ ] 初始数据已加载
- [ ] 错误处理已设置

### [LIFECYCLE_02] 组件清理完整性
**规则**: 每个组件必须在onUnmounted中清理所有资源

```typescript
// ✅ 正确的清理模式
onUnmounted(() => {
  // 1. 断开外部连接
  if (sseConnection.value) {
    sseService.disconnect()
  }
  
  // 2. 移除事件监听
  window.removeEventListener('custom-event', handler)
  
  // 3. 清理定时器
  if (timer.value) {
    clearInterval(timer.value)
  }
  
  // 4. 取消未完成的请求
  abortController.abort()
  
  // 5. 清理状态
  resetComponentState()
})
```

**检查清单**:
- [ ] 所有外部连接已断开
- [ ] 所有事件监听器已移除
- [ ] 所有定时器已清理
- [ ] 所有异步操作已取消
- [ ] 组件状态已重置

### [LIFECYCLE_03] 父子组件通信规范
**规则**: 子组件事件必须被父组件正确监听和处理

```vue
<!-- ✅ 正确的事件监听模式 -->
<template>
  <ChildComponent
    :prop="value"
    @close="handleClose"
    @error="handleError"
    @update="handleUpdate"
  />
</template>

<script setup>
// 必须为每个子组件事件提供处理器
const handleClose = () => {
  // 处理关闭逻辑
  resetParentState()
  navigateToDefault()
}

const handleError = (error: Error) => {
  // 处理错误
  showErrorMessage(error.message)
}
</script>
```

**检查清单**:
- [ ] 所有子组件emit事件都有对应的监听器
- [ ] 事件处理器实现了完整的业务逻辑
- [ ] 错误事件有适当的错误处理
- [ ] 状态变更事件更新了父组件状态

### [LIFECYCLE_04] 异步操作管理
**规则**: 组件中的异步操作必须正确管理生命周期

```typescript
// ✅ 正确的异步操作管理
const abortController = ref<AbortController>()

const loadData = async () => {
  abortController.value = new AbortController()
  
  try {
    const response = await fetch('/api/data', {
      signal: abortController.value.signal
    })
    // 处理响应
  } catch (error) {
    if (error.name !== 'AbortError') {
      handleError(error)
    }
  }
}

onUnmounted(() => {
  abortController.value?.abort()
})
```

## 预防性措施

### [PREV_01] 生命周期检查器
```typescript
// 开发工具：组件生命周期检查器
export function useLifecycleChecker(componentName: string) {
  const resources = new Set<string>()
  
  const registerResource = (name: string) => {
    resources.add(name)
    console.log(`[${componentName}] Resource registered: ${name}`)
  }
  
  const unregisterResource = (name: string) => {
    resources.delete(name)
    console.log(`[${componentName}] Resource cleaned: ${name}`)
  }
  
  onUnmounted(() => {
    if (resources.size > 0) {
      console.warn(`[${componentName}] Leaked resources:`, Array.from(resources))
    }
  })
  
  return { registerResource, unregisterResource }
}
```

### [PREV_02] ESLint规则
```javascript
// .eslintrc.js
rules: {
  'vue/require-cleanup-in-unmounted': 'error',
  'vue/require-event-handlers': 'error',
  'vue/no-unused-emit': 'error'
}
```

### [PREV_03] 组件模板
```vue
<!-- 组件模板：确保生命周期完整性 -->
<template>
  <!-- 组件内容 -->
</template>

<script setup lang="ts">
// 1. 导入和类型定义
import { onMounted, onUnmounted, ref } from 'vue'

// 2. Props和Emits定义
interface Props {
  // props定义
}

interface Emits {
  close: []
  error: [error: Error]
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 3. 响应式状态
const state = ref()

// 4. 生命周期钩子
onMounted(() => {
  // 初始化逻辑
})

onUnmounted(() => {
  // 清理逻辑
})

// 5. 方法定义
const handleAction = () => {
  // 方法实现
}
</script>
```

## 违规示例与修复

### 违规示例1: 缺失事件监听
```vue
<!-- ❌ 错误：子组件事件未监听 -->
<ReportViewer
  :report="report"
  :task="task"
  <!-- 缺失 @close 监听 -->
/>

<!-- ✅ 正确：完整的事件监听 -->
<ReportViewer
  :report="report"
  :task="task"
  @close="handleReportClose"
  @error="handleReportError"
/>
```

### 违规示例2: 资源泄漏
```typescript
// ❌ 错误：未清理资源
onMounted(() => {
  const interval = setInterval(updateData, 1000)
  window.addEventListener('resize', handleResize)
  // 缺失清理逻辑
})

// ✅ 正确：完整的资源管理
const interval = ref<NodeJS.Timeout>()

onMounted(() => {
  interval.value = setInterval(updateData, 1000)
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  if (interval.value) {
    clearInterval(interval.value)
  }
  window.removeEventListener('resize', handleResize)
})
```

## 自动化验证

### 生命周期验证器
```typescript
// scripts/lifecycle_validator.ts
export function validateComponentLifecycle(componentPath: string) {
  const content = readFileSync(componentPath, 'utf-8')
  
  const checks = [
    checkMountedHook(content),
    checkUnmountedHook(content),
    checkEventListeners(content),
    checkResourceCleanup(content)
  ]
  
  return checks.every(check => check.passed)
}
```

### CI/CD集成
```yaml
# .github/workflows/component-validation.yml
- name: Validate Component Lifecycle
  run: |
    npm run validate:lifecycle
    npm run test:lifecycle
```

## 度量指标

### 生命周期健康度
- 资源泄漏率: 0%
- 事件监听覆盖率: 100%
- 清理完整性: 100%

### 质量门禁
- 所有组件必须通过生命周期验证
- 资源泄漏检测必须通过
- 事件处理覆盖率必须达到100%
