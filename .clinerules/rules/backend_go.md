# Go Backend Rules

*   **[GO_01 | Variable Scoping]:** Use ':=' for declaration and initialization ONLY. Use '=' for assigning to existing variables, especially for 'err' within 'if' blocks or loops. (From GO001)
*   **[GO_02 | Single Main Function]:** A 'main' package MUST contain only one 'main' function across all files in that package directory. (From GO002)
*   **[GO_03 | GORM Eager Loading]:** When querying GORM models, MUST use `.Preload("AssociationName")` to load required related data. When using `.Select(...)`, MUST ensure all required fields (especially `ID` if the object or its ID is used later) are included in the selection. (From GO003)
*   **[GO_04 | API Call Abstraction]:** All calls to external APIs (e.g., LLM) MUST be wrapped in utility functions that handle configuration, timeouts, retries, and standardized error logging. (From GO004)
*   **[GO_05 | Type-Safe Error Handling]:** Use Go's type-safe error handling patterns (errors.As(), errors.Is()) for error classification instead of string-based error detection. Define custom error types for different error categories. (From Worker Optimization 2025-07-02)
*   **[GO_06 | Error Classification]:** Distinguish between different error types in application logic: system errors (require logging/alerting), business conditions (normal states), and recoverable errors (retry logic). (From Worker Optimization 2025-07-02)
*   **[GO_07 | Database Parameter Compatibility]:** When supporting multiple database types, use appropriate parameter placeholder syntax: PostgreSQL ($1, $2, $3) vs SQLite (?, ?, ?). Implement database-specific query building when necessary. (From Worker Optimization 2025-07-02)

# Error Handling Rules

*   **[ERR_01 | Custom Error Types]:** Define custom error types for different error categories:
    ```go
    // Business condition errors (not system failures)
    type NoTaskAvailableError struct{}
    func (e *NoTaskAvailableError) Error() string { return "no task available" }

    // System errors (require logging and alerting)
    type DatabaseConnectionError struct{ Cause error }
    func (e *DatabaseConnectionError) Error() string { return fmt.Sprintf("database connection failed: %v", e.Cause) }

    // Recoverable errors (can be retried)
    type TemporaryServiceError struct{ Service string; Cause error }
    func (e *TemporaryServiceError) Error() string { return fmt.Sprintf("%s temporarily unavailable: %v", e.Service, e.Cause) }
    ```

*   **[ERR_02 | Type-Safe Error Detection]:** Use errors.As() and errors.Is() for error type checking:
    ```go
    // Preferred: Type-safe error detection
    var noTaskErr *NoTaskAvailableError
    if errors.As(err, &noTaskErr) {
        // Handle as normal business condition
        return nil
    }

    var tempErr *TemporaryServiceError
    if errors.As(err, &tempErr) {
        // Implement retry logic
        return r.retryWithBackoff(tempErr.Service)
    }

    // For standard library errors
    if errors.Is(err, context.DeadlineExceeded) {
        // Handle timeout specifically
    }
    ```

*   **[ERR_03 | Error Wrapping]:** Use fmt.Errorf with %w verb to wrap errors while preserving the error chain:
    ```go
    if err != nil {
        return fmt.Errorf("failed to process task %s: %w", taskID, err)
    }
    ```

*   **[ERR_04 | Logging Strategy]:** Apply different logging strategies based on error type:
    ```go
    // System errors: ERROR level with full context
    if isSystemError(err) {
        logger.WithError(err).WithField("task_id", taskID).Error("System failure occurred")
    }

    // Business conditions: DEBUG level or no logging
    if isBusinessCondition(err) {
        logger.WithField("condition", err.Error()).Debug("Normal business condition")
    }

    // Recoverable errors: WARN level with retry information
    if isRecoverableError(err) {
        logger.WithError(err).WithField("retry_count", retryCount).Warn("Recoverable error, will retry")
    }
    ```

# Database Interaction Rules

*   **[DB_01 | GORM AutoMigrate Limitations]:** GORM AutoMigrate does not drop columns, modify existing column constraints, or rename columns. Manual SQL or specific migration steps are required for these operations. (From DB001)
*   **[DB_02 | Explicit Migration Checks]:** `AutoMigrate` might not reliably add columns/indexes to existing tables. Consider explicit checks (`Migrator().HasColumn`, `AddColumn`) after `AutoMigrate` for critical schema changes. (From DB002)
*   **[DB_03 | Data Validation Before Persistence]:** Data received from external sources MUST be cleaned/validated before persistence, checking for empty strings or placeholders to avoid storing invalid data. (From DB003)
*   **[DB_04 | Native SQL for Vector Ops]:** Vector database operations (generation, search, indexing) MUST use native SQL queries as GORM may not fully support them. (From DB004)
*   **[DB_05 | Vector Indexing]:** Vector columns MUST have an appropriate index (e.g., ANN index) created explicitly during migration for performance. (From DB005)
