# API Governance Rules and Standards

## Core Principles

### [API_GOV_01 | Documentation-First Development]
**Rule**: All API implementations MUST strictly follow documented specifications.
**Process**:
1. Verify API endpoint exists in backend documentation before frontend implementation
2. Match data structures exactly between documentation and implementation
3. Use consistent naming conventions across frontend and backend
4. Implement version management with proper prefixes

### [API_GOV_02 | Contract-Driven Development]
**Rule**: API contracts MUST be validated at runtime and development time.
**Implementation**:
- Define TypeScript interfaces matching backend models exactly
- Implement runtime validation for API responses
- Use automated tools to detect contract violations
- Maintain contract test suites

## Frontend-Backend Consistency Rules

### [API_GOV_03 | Path Consistency Enforcement]
**Rule**: Frontend API paths MUST match backend route definitions exactly.
**Verification**:
- Use centralized path constants with version prefixes
- Implement automated path consistency checking
- Map dynamic parameters correctly (e.g., `{case_id}` → `${caseId}`)

**Example**:
```typescript
// ✅ Correct - Matches backend /v1/cases/{case_id}
export const CASE_PATHS = {
  GET_CASE_DETAIL: (caseId: string) => `/v1/cases/${caseId}`,
} as const;

// ❌ Incorrect - Missing version prefix
const path = `/cases/${caseId}`;
```

### [API_GOV_04 | Data Model Synchronization]
**Rule**: Frontend data models MUST align with backend data structures or implement proper transformation.
**Strategies**:
1. **Direct Mapping**: When structures match exactly
2. **Transformation Layer**: When adaptation is needed
3. **Hybrid Approach**: Combine both strategies as needed

**Transformation Example**:
```typescript
// Backend model
interface AdjudicationLead {
  lead_id: string;
  filename: string;
  status: TaskStatus;
}

// Frontend model
interface Case {
  id: string;
  candidateName: string;
  status: CaseStatus;
}

// Transformation function
const transformApiCase = (apiLead: AdjudicationLead): Case => ({
  id: apiLead.lead_id,
  candidateName: extractCandidateName(apiLead.filename),
  status: mapTaskStatusToCaseStatus(apiLead.status),
});
```

### [API_GOV_05 | Status and Enum Mapping]
**Rule**: Enum values MUST be mapped consistently between frontend and backend.
**Implementation**:
- Create explicit mapping functions for status conversions
- Handle unknown/new enum values gracefully
- Document mapping logic clearly

## Automated Governance Tools

### [API_GOV_06 | Continuous Consistency Checking]
**Rule**: Implement automated tools to monitor API consistency continuously.
**Required Tools**:
1. **API Sync Checker**: `scripts/checkApiSync.cjs`
2. **Contract Validator**: `src/utils/apiContract.ts`
3. **Report Generator**: `scripts/generateApiReport.cjs`
4. **Type Validator**: TypeScript compiler checks

**NPM Scripts**:
```json
{
  "check:api": "node scripts/checkApiSync.cjs",
  "check:api:watch": "nodemon --watch specs --watch src --ext md,ts,tsx --exec \"npm run check:api\"",
  "validate:types": "tsc --noEmit --skipLibCheck",
  "generate:api-report": "node scripts/generateApiReport.cjs"
}
```

### [API_GOV_07 | Development Workflow Integration]
**Rule**: API governance checks MUST be integrated into the development workflow.
**Integration Points**:
- Pre-commit hooks for API consistency
- Code review checklist items
- CI/CD pipeline validation
- Development server warnings

## Error Handling and Recovery

### [API_GOV_08 | Graceful Degradation]
**Rule**: Frontend MUST handle API inconsistencies gracefully.
**Strategies**:
- Implement fallback mechanisms for missing endpoints
- Use mock data when backend is unavailable
- Display user-friendly error messages
- Log detailed error information for debugging

### [API_GOV_09 | Version Migration Support]
**Rule**: Support smooth migration between API versions.
**Implementation**:
- Maintain backward compatibility during transitions
- Implement version detection and routing
- Provide clear migration paths
- Document breaking changes thoroughly

## Quality Assurance

### [API_GOV_10 | Testing Requirements]
**Rule**: API integrations MUST have comprehensive test coverage.
**Test Types**:
1. **Contract Tests**: Verify API response structures
2. **Integration Tests**: Test end-to-end API flows
3. **Mock Tests**: Test with simulated backend responses
4. **Error Scenario Tests**: Test error handling paths

### [API_GOV_11 | Documentation Maintenance]
**Rule**: API documentation MUST be kept synchronized with implementation.
**Process**:
1. Update documentation before implementing changes
2. Review documentation during code reviews
3. Generate API reports regularly
4. Maintain change logs for API modifications

## Monitoring and Metrics

### [API_GOV_12 | Governance Metrics]
**Rule**: Track and monitor API governance metrics.
**Key Metrics**:
- API path consistency rate (target: 100%)
- Type coverage rate (target: 100%)
- Contract violation frequency (target: 0)
- Documentation synchronization rate (target: 100%)

### [API_GOV_13 | Alerting and Notifications]
**Rule**: Implement alerting for API governance violations.
**Alert Triggers**:
- Contract validation failures
- Path consistency violations
- Type definition mismatches
- Documentation drift detection

## Best Practices

### [API_GOV_14 | Proactive Problem Prevention]
**Rule**: Prevent API inconsistencies through proactive measures.
**Strategies**:
- Use code generation from API specifications
- Implement shared type definitions
- Establish clear communication channels between teams
- Regular cross-team API reviews

### [API_GOV_15 | Knowledge Sharing]
**Rule**: Share API governance knowledge across the team.
**Methods**:
- Document lessons learned in `.clinerules`
- Conduct API governance training sessions
- Maintain troubleshooting guides
- Create onboarding materials for new developers

---

## Implementation Checklist

When implementing API governance:

- [ ] Create centralized API path constants
- [ ] Define complete TypeScript type definitions
- [ ] Implement runtime contract validation
- [ ] Set up automated consistency checking
- [ ] Integrate checks into development workflow
- [ ] Create comprehensive test coverage
- [ ] Establish monitoring and alerting
- [ ] Document governance processes
- [ ] Train team on governance practices
- [ ] Regular review and improvement cycles
