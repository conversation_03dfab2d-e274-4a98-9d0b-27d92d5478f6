# API一致性管理规则
# Version: 1.0.0
# Created: 2025-07-09
# Purpose: 防止前后端API端点不一致问题

## 问题根因分析

### 发现的问题
1. **端点路径不匹配**: 前端 `/api/v1/sse/events` vs 后端 `/v1/events`
2. **文档与实现脱节**: 设计文档中的端点与实际代码不同步
3. **缺乏自动化验证**: 没有工具检测前后端API契约一致性

### 根本原因
- **[RC_01]** 缺乏统一的API端点定义源
- **[RC_02]** 前后端开发独立进行，缺乏交叉验证
- **[RC_03]** API变更时未同步更新所有相关文件
- **[RC_04]** 缺乏自动化的API契约测试

## 强制性规则

### [API_01] 统一端点定义源
**规则**: 所有API端点必须在单一配置文件中定义，前后端共享
```yaml
# config/api_endpoints.yaml
endpoints:
  sse:
    path: "/v1/events"
    method: "GET"
    auth_required: false
  upload:
    path: "/v1/upload"
    method: "POST"
    auth_required: true
```

**检查清单**:
- [ ] API端点配置文件存在且完整
- [ ] 前端使用配置文件中的端点定义
- [ ] 后端路由与配置文件一致
- [ ] 文档引用配置文件中的端点

### [API_02] 前后端端点验证
**规则**: 每次API变更必须通过自动化验证
```bash
# 验证脚本示例
./scripts/validate_api_consistency.sh
```

**检查清单**:
- [ ] 前端API调用路径与后端路由匹配
- [ ] HTTP方法一致
- [ ] 请求/响应格式匹配
- [ ] 认证要求一致

### [API_03] 版本化管理
**规则**: API端点变更必须遵循版本化策略
- 破坏性变更: 增加版本号 (v1 -> v2)
- 兼容性变更: 保持版本号，添加可选参数
- 废弃端点: 标记deprecated，设置移除时间表

### [API_04] 文档同步强制
**规则**: API变更时必须同时更新以下文件
- [ ] API端点配置文件
- [ ] 前端API调用代码
- [ ] 后端路由定义
- [ ] API文档
- [ ] 测试用例

## 预防性措施

### [PREV_01] 开发前验证
```bash
# 开发开始前运行
npm run api:validate
go run scripts/api_check.go
```

### [PREV_02] 提交前钩子
```bash
# .git/hooks/pre-commit
#!/bin/bash
./scripts/validate_api_consistency.sh || exit 1
```

### [PREV_03] CI/CD集成
```yaml
# .github/workflows/api-validation.yml
- name: Validate API Consistency
  run: |
    npm run api:validate
    go test ./tests/api_contract_test.go
```

## 违规示例与修复

### 违规示例1: 硬编码端点
```typescript
// ❌ 错误做法
const SSE_URL = 'http://localhost:8080/api/v1/sse/events'

// ✅ 正确做法
import { API_ENDPOINTS } from '@/config/api'
const SSE_URL = `${API_BASE_URL}${API_ENDPOINTS.sse.path}`
```

### 违规示例2: 路由与文档不一致
```go
// ❌ 错误：代码与文档不匹配
router.GET("/v1/events", handleSSE)  // 实际代码
// 文档中写的是 /v1/sse/events

// ✅ 正确：使用配置驱动
router.GET(config.API.SSE.Path, handleSSE)
```

## 自动化工具

### API一致性检查器
```python
# scripts/api_consistency_checker.py
def validate_api_consistency():
    frontend_apis = extract_frontend_api_calls()
    backend_routes = extract_backend_routes()
    config_endpoints = load_api_config()
    
    return check_consistency(frontend_apis, backend_routes, config_endpoints)
```

### 实时监控
```javascript
// 开发环境API调用监控
if (process.env.NODE_ENV === 'development') {
  interceptApiCalls((url, method) => {
    validateAgainstConfig(url, method)
  })
}
```

## 应急修复流程

### 发现不一致时的处理步骤
1. **立即停止**: 停止相关功能的开发
2. **根因分析**: 确定不一致的具体原因
3. **统一修复**: 以配置文件为准，修复所有不一致
4. **验证测试**: 运行完整的API一致性测试
5. **文档更新**: 更新所有相关文档
6. **流程改进**: 分析为什么现有流程没有捕获问题

## 度量指标

### 一致性指标
- API端点匹配率: 100%
- 文档同步率: 100%
- 自动化覆盖率: >95%

### 质量门禁
- 所有API一致性检查必须通过
- 文档与代码同步验证必须通过
- 端到端API测试必须通过

## 工具集成

### IDE插件
- VSCode扩展：实时检查API调用与配置的一致性
- GoLand插件：验证路由定义与配置匹配

### 监控告警
- 生产环境API调用404错误告警
- 开发环境端点不匹配告警
