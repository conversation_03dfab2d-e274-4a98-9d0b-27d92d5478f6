# 代码与文档同步规则
# Version: 1.0.0
# Created: 2025-07-09
# Purpose: 确保代码实现与设计文档保持同步，防止文档与实现脱节

## 问题根因分析

### 发现的问题
1. **文档滞后**: 代码已实现但文档未更新
2. **实现偏离**: 实际实现与设计文档不一致
3. **缺乏验证**: 没有自动化工具检查文档与代码的一致性

### 根本原因
- **[DS_01]** 文档更新不在开发流程中
- **[DS_02]** 缺乏文档与代码的关联机制
- **[DS_03]** 没有强制性的同步检查
- **[DS_04]** 文档责任归属不明确

## 强制性规则

### [DOC_SYNC_01] 同步更新强制
**规则**: 任何代码变更必须同时更新相关文档

**影响范围映射**:
```yaml
# .clinerules/doc_sync_mapping.yaml
code_to_docs:
  "internal/handlers/*.go":
    - "plans/implementation/api_specification.md"
    - "docs/api/endpoints.md"
  "src/services/*.ts":
    - "plans/frontend/service_architecture.md"
    - "docs/frontend/services.md"
  "cmd/*/main.go":
    - "plans/deployment/service_configuration.md"
    - "docs/deployment/services.md"
```

**检查清单**:
- [ ] 相关设计文档已更新
- [ ] API文档已同步
- [ ] 配置文档已更新
- [ ] 部署文档已修改
- [ ] 用户文档已调整

### [DOC_SYNC_02] 文档驱动开发
**规则**: 重大功能变更必须先更新文档，再实现代码

```markdown
# 变更流程
1. 更新设计文档 → 2. 代码评审文档 → 3. 实现代码 → 4. 验证一致性
```

**文档优先检查**:
- [ ] 功能设计文档已完成
- [ ] API规范已定义
- [ ] 数据模型已设计
- [ ] 错误处理已规划
- [ ] 测试策略已制定

### [DOC_SYNC_03] 自动化一致性验证
**规则**: 每次提交必须通过文档与代码一致性检查

```bash
# 一致性检查脚本
./scripts/validate_doc_code_sync.sh
```

**验证项目**:
- [ ] API端点与文档一致
- [ ] 数据结构与文档匹配
- [ ] 配置项与文档同步
- [ ] 错误码与文档对应
- [ ] 版本号与文档一致

### [DOC_SYNC_04] 文档版本管理
**规则**: 文档必须与代码使用相同的版本控制策略

```yaml
# 版本同步策略
documentation:
  versioning: "semantic"  # 语义化版本
  sync_with: "code"       # 与代码同步
  update_trigger: "code_change"  # 代码变更触发
```

## 预防性措施

### [PREV_01] 提交前检查
```bash
# .git/hooks/pre-commit
#!/bin/bash
echo "Checking documentation sync..."

# 检查是否有代码变更但文档未更新
./scripts/check_doc_sync.sh || {
  echo "Error: Code changes detected but related documentation not updated"
  echo "Please update the following documents:"
  ./scripts/list_affected_docs.sh
  exit 1
}
```

### [PREV_02] PR模板强制
```markdown
<!-- .github/pull_request_template.md -->
## 文档同步检查清单
- [ ] 相关设计文档已更新
- [ ] API文档已同步（如适用）
- [ ] 配置文档已更新（如适用）
- [ ] 用户文档已调整（如适用）
- [ ] 运行了文档一致性检查

## 文档变更说明
请描述对文档的具体变更：
- 
```

### [PREV_03] 自动化文档生成
```typescript
// 自动生成API文档
/**
 * @api {POST} /v1/upload 上传文件
 * @apiName UploadFile
 * @apiGroup File
 * @apiParam {File} file 要上传的文件
 * @apiSuccess {String} lead_id 任务ID
 */
export async function uploadFile(file: File): Promise<UploadResponse> {
  // 实现
}
```

## 违规示例与修复

### 违规示例1: 代码与文档不一致
```go
// ❌ 错误：实现与文档不匹配
// 文档说是 /v1/sse/events，但实现是 /v1/events
router.GET("/v1/events", handleSSE)

// ✅ 正确：实现与文档保持一致
// 1. 先更新文档，明确端点为 /v1/events
// 2. 实现代码与文档一致
router.GET("/v1/events", handleSSE)  // 与文档匹配
```

### 违规示例2: 缺失文档更新
```typescript
// ❌ 错误：添加新功能但未更新文档
export interface TaskStatus {
  id: string
  status: 'pending' | 'processing' | 'completed' | 'failed'
  // 新增字段但文档未更新
  priority: number  
}

// ✅ 正确：同时更新代码和文档
// 1. 更新 plans/implementation/data_models.md
// 2. 更新 docs/api/types.md
// 3. 实现代码变更
export interface TaskStatus {
  id: string
  status: 'pending' | 'processing' | 'completed' | 'failed'
  priority: number  // 已在文档中说明
}
```

## 自动化工具

### 文档同步检查器
```python
# scripts/doc_sync_checker.py
import re
import yaml
from pathlib import Path

class DocSyncChecker:
    def __init__(self, config_path: str):
        self.mapping = yaml.safe_load(Path(config_path).read_text())
    
    def check_api_endpoints(self):
        """检查API端点一致性"""
        backend_routes = self.extract_routes()
        frontend_calls = self.extract_api_calls()
        doc_endpoints = self.extract_doc_endpoints()
        
        return self.validate_consistency(backend_routes, frontend_calls, doc_endpoints)
    
    def check_data_models(self):
        """检查数据模型一致性"""
        go_structs = self.extract_go_structs()
        ts_interfaces = self.extract_ts_interfaces()
        doc_models = self.extract_doc_models()
        
        return self.validate_model_consistency(go_structs, ts_interfaces, doc_models)
```

### 实时监控
```javascript
// 开发环境文档同步监控
if (process.env.NODE_ENV === 'development') {
  const watcher = chokidar.watch(['src/**/*.ts', 'docs/**/*.md'])
  
  watcher.on('change', (path) => {
    if (path.endsWith('.ts')) {
      checkRelatedDocs(path)
    }
  })
}
```

### CI/CD集成
```yaml
# .github/workflows/doc-sync.yml
name: Documentation Sync Check

on: [push, pull_request]

jobs:
  doc-sync:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      
      - name: Check Documentation Sync
        run: |
          python scripts/doc_sync_checker.py
          
      - name: Validate API Documentation
        run: |
          npm run validate:api-docs
          
      - name: Check Code-Doc Consistency
        run: |
          ./scripts/validate_doc_code_sync.sh
```

## 文档质量标准

### [QUALITY_01] 文档完整性
**要求**: 每个功能模块必须有完整的文档覆盖
- [ ] 功能概述
- [ ] API规范
- [ ] 数据模型
- [ ] 错误处理
- [ ] 使用示例

### [QUALITY_02] 文档准确性
**要求**: 文档内容必须与实际实现100%匹配
- [ ] API端点正确
- [ ] 参数类型准确
- [ ] 响应格式匹配
- [ ] 错误码对应
- [ ] 示例可执行

### [QUALITY_03] 文档时效性
**要求**: 文档更新必须与代码变更同步
- [ ] 变更当天更新
- [ ] 版本号同步
- [ ] 废弃标记及时
- [ ] 新功能文档完整

## 度量指标

### 同步性指标
- 文档代码一致性: 100%
- 文档更新及时性: 24小时内
- 文档覆盖率: >95%

### 质量门禁
- 文档同步检查必须通过
- API文档验证必须通过
- 代码文档一致性必须达到100%

## 应急处理

### 发现不同步时的处理流程
1. **立即标记**: 在相关文档中添加"待同步"标记
2. **影响评估**: 评估不同步对开发和用户的影响
3. **优先修复**: 根据影响程度确定修复优先级
4. **同步更新**: 统一更新所有相关文档和代码
5. **验证测试**: 运行完整的同步性验证
6. **流程改进**: 分析原因，改进同步流程
