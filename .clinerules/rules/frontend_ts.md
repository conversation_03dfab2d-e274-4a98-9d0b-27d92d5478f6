# TypeScript Frontend Rules

## API Integration Rules

### [FE_API_01 | API Key Authentication]
**Rule**: All authenticated API requests MUST include the API key in the `X-API-Key` header.
**Implementation**: 
- Configure API key in request interceptors
- Use environment variables for API key configuration
- Ensure API key is included in all protected API calls

**Example**:
```typescript
// ✅ Correct
instance.interceptors.request.use((config) => {
  config.headers['X-API-Key'] = import.meta.env.VITE_API_KEY;
  return config;
});

// ❌ Incorrect - Missing API key
axios.get('/api/protected-endpoint');
```

### [FE_API_02 | CORS Headers Compatibility]
**Rule**: Frontend request headers MUST be compatible with backend CORS configuration.
**Common Headers**: `X-Request-ID`, `X-API-Key`, `Content-Type`, `Authorization`
**Verification**: Ensure all custom headers are listed in backend's `Access-Control-Allow-Headers`

### [FE_API_03 | Request ID Tracking]
**Rule**: All API requests SHOULD include a unique `X-Request-ID` header for tracing.
**Implementation**: Generate UUID or timestamp-based request IDs in interceptors.

## Component and Layout Rules

### [FE_LAYOUT_01 | Ant Design Layout Structure]
**Rule**: When using Ant Design Layout, avoid setting `width: 0` on `.ant-layout-content`.
**Reason**: This breaks responsive design and content visibility.
**Correct Approach**: Use proper flex or grid layouts with appropriate width values.
**Fix**: Add CSS overrides to prevent Ant Design's default flexbox conflicts:
```css
.ant-layout.ant-layout-has-sider > .ant-layout,
.ant-layout.ant-layout-has-sider > .ant-layout-content {
  width: auto !important;
  flex: none !important;
}
```

### [FE_LAYOUT_02 | Icon Import Completeness]
**Rule**: All Ant Design icons used in components MUST be explicitly imported.
**Common Missing Icons**: `MenuFoldOutlined`, `MenuUnfoldOutlined`, navigation icons
**Verification**: Check browser console for "is not defined" errors

**Example**:
```typescript
// ✅ Correct
import {
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  DashboardOutlined,
  UploadOutlined
} from '@ant-design/icons';

// ❌ Incorrect - Missing imports
const Layout = () => <MenuFoldOutlined />; // ReferenceError
```

### [FE_LAYOUT_03 | Responsive Design Considerations]
**Rule**: Layout components MUST handle mobile and desktop views appropriately.
**Implementation**: Use CSS media queries and Ant Design's responsive utilities.

## Environment Configuration Rules

### [FE_ENV_01 | Environment Variables]
**Rule**: All configuration values MUST use environment variables with `VITE_` prefix.
**Required Variables**:
- `VITE_API_BASE_URL`: Backend API base URL
- `VITE_API_KEY`: API authentication key
- `VITE_APP_TITLE`: Application title

### [FE_ENV_02 | Development vs Production Config]
**Rule**: Environment-specific configurations MUST be properly separated.
**Implementation**: Use `.env`, `.env.development`, `.env.production` files.

## Error Handling Rules

### [FE_ERROR_01 | API Error Handling]
**Rule**: All API calls MUST have proper error handling with user-friendly messages.
**Implementation**: Use response interceptors to handle common error scenarios.

### [FE_ERROR_02 | Network Error Recovery]
**Rule**: Network failures MUST be handled gracefully with retry mechanisms where appropriate.

## State Management Rules

### [FE_STATE_01 | Store Structure]
**Rule**: State stores MUST follow consistent patterns for actions, state, and getters.
**Implementation**: Use TypeScript interfaces for type safety.

### [FE_STATE_02 | API State Synchronization]
**Rule**: Frontend state MUST be synchronized with backend state through proper API calls.

## Development and Debugging Rules

### [FE_DEBUG_01 | Console Logging]
**Rule**: API requests and responses SHOULD be logged in development mode.
**Implementation**: Use conditional logging based on environment.

### [FE_DEBUG_02 | Hot Reload Compatibility]
**Rule**: Code changes MUST be compatible with Vite's hot module replacement.
**Common Issues**: Avoid circular dependencies, ensure proper module exports.

## Testing Rules

### [FE_TEST_01 | Component Testing]
**Rule**: Critical UI components MUST have unit tests covering main functionality.

### [FE_TEST_02 | API Integration Testing]
**Rule**: API integration points MUST have tests covering success and error scenarios.

## Performance Rules

### [FE_PERF_01 | Bundle Size Optimization]
**Rule**: Avoid importing entire libraries when only specific functions are needed.
**Example**: Import specific Ant Design components instead of the entire library.

### [FE_PERF_02 | Lazy Loading]
**Rule**: Route components SHOULD be lazy-loaded to improve initial page load time.

## Security Rules

### [FE_SEC_01 | API Key Protection]
**Rule**: API keys MUST NOT be exposed in client-side code in production.
**Implementation**: Use environment variables and proper build-time substitution.
**Example**:
```typescript
// ❌ Wrong - hardcoded API key
const apiKey = 'e83d68ad6a516434f504bd05419f88597b68bfa92ec6cde811f026a1e4f9c476';

// ✅ Correct - environment variable
const apiKey = import.meta.env.VITE_API_KEY;
```

### [FE_SEC_02 | Input Validation]
**Rule**: All user inputs MUST be validated on the frontend before sending to backend.

## API Contract and Governance Rules

### [FE_API_04 | API Path Constants Management]
**Rule**: All API paths MUST be defined as constants in `src/constants/apiPaths.ts` and include version prefixes.
**Implementation**:
- Use centralized path management with version prefixes (e.g., `/v1/`)
- Create typed path constants with parameter functions
- Never hardcode API paths in components or services

**Example**:
```typescript
// ✅ Correct
export const CASE_PATHS = {
  CREATE_CASE: buildPath('/cases'),
  GET_CASE_DETAIL: (caseId: string) => buildPath(`/cases/${caseId}`),
} as const;

// ❌ Incorrect - Hardcoded paths
await apiClient.get('/cases');
```

### [FE_API_05 | Type-Safe API Calls]
**Rule**: All API calls MUST use TypeScript generics to specify request/response types.
**Implementation**: Define complete API types in `src/types/api.ts` matching backend documentation exactly.

**Example**:
```typescript
// ✅ Correct
const response = await apiClient.get<GetCasesResponse>(CASE_PATHS.GET_CASES);

// ❌ Incorrect - No type specification
const response = await apiClient.get('/v1/cases');
```

### [FE_API_06 | API Contract Validation]
**Rule**: Implement runtime API contract validation to detect backend/frontend mismatches.
**Implementation**:
- Use contract validation middleware in API client
- Validate response structure against expected types
- Log contract violations for debugging

### [FE_API_07 | Data Model Adaptation]
**Rule**: When backend data models don't match frontend expectations, create adaptation layers instead of forcing changes.
**Implementation**:
- Create transformation functions to convert backend data to frontend models
- Implement status mapping functions for enum conversions
- Handle missing fields gracefully with default values

**Example**:
```typescript
// ✅ Correct - Data transformation
const transformApiCase = (apiLead: AdjudicationLead): Case => ({
  id: apiLead.lead_id,
  candidateName: extractCandidateName(apiLead.filename),
  status: mapTaskStatusToCaseStatus(apiLead.status),
});

// ❌ Incorrect - Direct assignment without transformation
const case = apiLead; // Type mismatch
```

### [FE_API_08 | Automated API Consistency Checking]
**Rule**: Use automated tools to verify API consistency between frontend and backend.
**Implementation**:
- Run `npm run check:api` before commits
- Use `npm run check:api:watch` during development
- Integrate API checks into CI/CD pipeline

### [FE_API_09 | API Documentation Synchronization]
**Rule**: Frontend API implementations MUST stay synchronized with backend documentation.
**Process**:
1. Check backend API documentation before implementing new features
2. Verify endpoint existence and data structures
3. Update type definitions when backend changes
4. Run consistency checks after updates

## TypeScript Best Practices

### [FE_TYPE_01 | TypeScript Import Types]
**Rule**: Avoid using `import type` for enums that need runtime values.
**Reason**: TypeScript `import type` imports are erased at runtime, causing "cannot be used as a value" errors.
**Solution**: Use regular imports for enums or define local enums:
```typescript
// ❌ Wrong - import type for runtime enum
import type { CaseStatus } from '@types/api';
const status = CaseStatus.COMPLETED; // Error!

// ✅ Correct - regular import or local enum
import { CaseStatus } from '@types/api';
// OR
export enum CaseStatus {
  COLLECTING = 'collecting',
  PROCESSING = 'processing'
}
```

## Testing and Quality Assurance

### [FE_TEST_01 | Backend Integration Testing]
**Rule**: Frontend projects SHOULD include backend API integration tests.
**Implementation**: Create test scripts that verify API endpoints, authentication, and error handling.
**Example**: Use npm scripts like `test:backend` to validate API connectivity and response formats.

### [FE_TEST_02 | Phase-Gate Development Testing]
**Rule**: Follow structured phase-gate testing approach for complex frontend development.
**Process**:
1. Phase 1: Document cross-validation and requirements verification
2. Phase 2: Code implementation review and compliance checking
3. Phase 2.5: Integration testing and end-to-end validation
4. Generate completion reports documenting findings and fixes

## Systematic Code Quality Rules

### [FE_CONCEPT_01 | Conceptual Model Consistency]
**Rule**: Maintain consistent conceptual models throughout the codebase to prevent confusion and technical debt.
**Implementation**:
- Use unified terminology across components, types, and documentation
- Avoid parallel concept systems (e.g., "Case" vs "Lead/Task")
- Implement systematic refactoring when concept models change
- Document concept evolution in .clinerules

**Example**:
```typescript
// ❌ Wrong - Mixed concepts
interface Case { lead_id: string; } // Uses lead_id but called Case
const CaseList = () => { /* processes AdjudicationLead */ };

// ✅ Correct - Unified concepts
interface AdjudicationLead { lead_id: string; }
const LeadList = () => { /* processes AdjudicationLead */ };
```

### [FE_CONCEPT_02 | Mock Data Alignment]
**Rule**: Mock data MUST exactly match backend API structures to prevent development-time masking of type issues.
**Implementation**:
- Remove duplicate fields that exist for "compatibility"
- Use identical field names and types as backend APIs
- Implement transformation layers when adaptation is needed
- Regularly validate mock data against actual API responses

**Example**:
```typescript
// ❌ Wrong - Duplicate fields mask type issues
const mockLead = {
  id: lead_id,        // Frontend compatibility field
  lead_id,           // Backend field
  // This masks the real type mismatch
};

// ✅ Correct - Exact backend alignment
const mockLead = {
  lead_id,           // Only backend field
  // Use transformation functions when needed
};
```

### [FE_CSS_01 | Modern Layout Architecture]
**Rule**: Use modern CSS layout systems (Grid/Flexbox) instead of hardcoded calculations to ensure maintainable responsive design.
**Implementation**:
- Replace `calc()` with fixed values with CSS Grid
- Avoid excessive `!important` declarations
- Use container queries for responsive design
- Implement semantic grid areas

**Example**:
```css
/* ❌ Wrong - Hardcoded calculations */
.center-panel {
  width: calc(100% - 600px) !important;
}

/* ✅ Correct - CSS Grid */
.workbench {
  display: grid;
  grid-template-columns: 300px 1fr 300px;
  grid-template-areas: "left center right";
}
.center-panel {
  grid-area: center;
}
```

### [FE_REFACTOR_01 | Systematic Legacy Cleanup]
**Rule**: When refactoring legacy code, implement systematic cleanup to prevent partial migrations and concept confusion.
**Process**:
1. **Audit Phase**: Identify all instances of legacy concepts
2. **Planning Phase**: Create comprehensive migration plan
3. **Execution Phase**: Implement changes systematically
4. **Validation Phase**: Verify no legacy remnants remain
5. **Documentation Phase**: Update .clinerules with lessons learned

**Tools**:
```bash
# Search for legacy concepts
grep -r "Case" src/ --include="*.ts" --include="*.tsx"
# Validate type consistency
npm run check:types
# Generate cleanup report
npm run generate:cleanup-report
```
