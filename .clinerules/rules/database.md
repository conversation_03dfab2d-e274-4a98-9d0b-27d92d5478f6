# Database Development Rules (数据库开发规则) v1.0

## 1. 数据库设计原则

### 1.1 架构设计
- **单一数据源**: 每个微服务拥有独立的数据库实例
- **读写分离**: 生产环境支持主从复制和读写分离
- **事务边界**: 明确定义事务边界，避免跨服务事务
- **数据一致性**: 优先考虑最终一致性，必要时使用强一致性

### 1.2 表设计规范
- **命名规范**: 表名使用复数形式，字段名使用snake_case
- **主键策略**: 优先使用UUID作为主键，避免自增ID
- **索引策略**: 为查询频繁的字段建立索引，避免过度索引
- **字段类型**: 选择合适的数据类型，避免过度设计

## 2. 数据库兼容性规则

### 2.1 多数据库支持策略
- **参数占位符兼容性**: 根据数据库类型使用正确的参数占位符
- **SQL方言差异**: 处理不同数据库的SQL语法差异
- **数据类型映射**: 确保数据类型在不同数据库间的兼容性
- **功能特性检测**: 运行时检测数据库特性支持情况

### 2.2 参数占位符规范
```go
// PostgreSQL风格 - 使用 $1, $2, $3...
query := `UPDATE table SET column1 = $1, column2 = $2 WHERE id = $3`
result, err := db.ExecContext(ctx, query, value1, value2, id)

// SQLite风格 - 使用 ?
query := `UPDATE table SET column1 = ?, column2 = ? WHERE id = ?`
result, err := db.ExecContext(ctx, query, value1, value2, id)

// 动态选择占位符风格
func (r *Repository) buildQuery(driver string) string {
    if driver == "postgres" {
        return `UPDATE table SET column1 = $1, column2 = $2 WHERE id = $3`
    }
    return `UPDATE table SET column1 = ?, column2 = ? WHERE id = ?`
}
```

### 2.3 数据库驱动检测
```go
// 基于连接字符串检测数据库类型
func detectDatabaseDriver(connStr string) string {
    if strings.HasPrefix(connStr, "sqlite://") || strings.HasPrefix(connStr, "sqlite:") {
        return "sqlite3"
    }
    return "postgres"
}

// 运行时获取驱动信息
func (r *Repository) getDriverName() string {
    if driver, ok := r.db.Driver().(*sqlite3.SQLiteDriver); ok {
        return "sqlite3"
    }
    return "postgres"
}
```

### 2.4 兼容性最佳实践
- **参数顺序一致性**: 确保参数传递顺序与占位符匹配
- **错误处理统一**: 统一处理不同数据库的错误类型
- **功能降级**: 为不支持的功能提供降级方案
- **测试覆盖**: 在所有支持的数据库上运行测试

## 3. PostgreSQL 特定规则

### 3.1 数据类型选择
```sql
-- 推荐的数据类型映射
TEXT        -- 字符串类型，避免使用VARCHAR(n)
JSONB       -- JSON数据，支持索引和查询
TIMESTAMP   -- 时间戳，统一使用UTC时区
UUID        -- 主键和外键
BIGINT      -- 大整数
BOOLEAN     -- 布尔值
```

## 4. SQLite 特定规则

### 4.1 SQLite数据类型映射
```sql
-- SQLite数据类型对应关系
TEXT        -- 字符串类型，对应PostgreSQL的TEXT
INTEGER     -- 整数类型，对应PostgreSQL的BIGINT
REAL        -- 浮点数类型
BLOB        -- 二进制数据
JSON        -- JSON数据 (SQLite 3.38+)
```

### 4.2 SQLite限制和解决方案
```sql
-- SQLite不支持的功能及替代方案

-- 1. 不支持JSONB，使用JSON或TEXT
-- PostgreSQL: JSONB类型
-- SQLite: TEXT类型存储JSON字符串

-- 2. 不支持数组类型，使用JSON数组
-- PostgreSQL: column_name TEXT[]
-- SQLite: column_name TEXT (存储JSON数组)

-- 3. 不支持ENUM类型，使用CHECK约束
-- PostgreSQL: status user_status
-- SQLite: status TEXT CHECK (status IN ('active', 'inactive'))

-- 4. 外键约束默认关闭，需要启用
PRAGMA foreign_keys = ON;
```

### 4.3 SQLite性能优化
```sql
-- SQLite性能优化设置
PRAGMA journal_mode = WAL;          -- 启用WAL模式
PRAGMA synchronous = NORMAL;        -- 平衡性能和安全性
PRAGMA cache_size = 10000;          -- 增加缓存大小
PRAGMA temp_store = memory;         -- 临时表存储在内存
PRAGMA mmap_size = 268435456;       -- 启用内存映射 (256MB)
```

### 2.2 索引策略
```sql
-- 单列索引
CREATE INDEX idx_table_column ON table_name (column_name);

-- 复合索引 (注意字段顺序)
CREATE INDEX idx_table_multi ON table_name (column1, column2);

-- JSONB索引
CREATE INDEX idx_table_jsonb ON table_name USING GIN (jsonb_column);

-- 部分索引
CREATE INDEX idx_table_partial ON table_name (column) WHERE condition;
```

### 2.3 约束定义
```sql
-- 主键约束
CONSTRAINT pk_table_name PRIMARY KEY (id)

-- 外键约束
CONSTRAINT fk_table_ref FOREIGN KEY (ref_id) REFERENCES ref_table(id)

-- 唯一约束
CONSTRAINT uk_table_unique UNIQUE (column1, column2)

-- 检查约束
CONSTRAINT ck_table_check CHECK (column > 0)
```

## 3. 迁移管理规则

### 3.1 迁移文件命名
```
格式: {序号}_{操作}_{表名}.{up|down}.sql
示例: 001_create_users.up.sql
     001_create_users.down.sql
```

### 3.2 迁移脚本规范
```sql
-- UP迁移示例
-- +migrate Up
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email TEXT NOT NULL UNIQUE,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW()
);

CREATE INDEX idx_users_email ON users (email);

-- DOWN迁移示例  
-- +migrate Down
DROP INDEX IF EXISTS idx_users_email;
DROP TABLE IF EXISTS users;
```

### 3.3 迁移最佳实践
- **向后兼容**: 新迁移不应破坏现有功能
- **原子操作**: 每个迁移文件应该是原子的
- **可回滚**: 每个UP迁移都必须有对应的DOWN迁移
- **测试验证**: 迁移前在测试环境验证

## 4. 查询优化规则

### 4.1 查询性能
```sql
-- 使用EXPLAIN分析查询计划
EXPLAIN (ANALYZE, BUFFERS) SELECT * FROM table WHERE condition;

-- 避免SELECT *，明确指定字段
SELECT id, name, email FROM users WHERE active = true;

-- 使用LIMIT限制结果集
SELECT * FROM large_table ORDER BY created_at DESC LIMIT 100;

-- 使用EXISTS代替IN (子查询)
SELECT * FROM users u WHERE EXISTS (
    SELECT 1 FROM orders o WHERE o.user_id = u.id
);
```

### 4.2 JSONB查询优化
```sql
-- 使用GIN索引加速JSONB查询
CREATE INDEX idx_data_gin ON table_name USING GIN (jsonb_column);

-- 高效的JSONB查询
SELECT * FROM table WHERE jsonb_column @> '{"key": "value"}';
SELECT * FROM table WHERE jsonb_column ? 'key';
SELECT * FROM table WHERE jsonb_column #> '{path,to,key}' = '"value"';
```

## 5. 事务管理规则

### 5.1 事务边界
```go
// 明确的事务边界
func (r *Repository) CreateUserWithProfile(ctx context.Context, user *User, profile *Profile) error {
    return r.db.WithTransaction(ctx, func(tx *sql.Tx) error {
        if err := r.createUser(ctx, tx, user); err != nil {
            return err
        }
        return r.createProfile(ctx, tx, profile)
    })
}
```

### 5.2 事务隔离级别
```sql
-- 根据需求选择合适的隔离级别
BEGIN TRANSACTION ISOLATION LEVEL READ COMMITTED;  -- 默认级别
BEGIN TRANSACTION ISOLATION LEVEL REPEATABLE READ; -- 避免不可重复读
BEGIN TRANSACTION ISOLATION LEVEL SERIALIZABLE;    -- 最高级别，性能影响大
```

## 6. 连接池配置

### 6.1 连接池参数
```go
// 推荐的连接池配置
config := &pgxpool.Config{
    MaxConns:        30,              // 最大连接数
    MinConns:        5,               // 最小连接数
    MaxConnLifetime: time.Hour,       // 连接最大生命周期
    MaxConnIdleTime: time.Minute * 30, // 连接最大空闲时间
}
```

### 6.2 连接管理
- **连接复用**: 使用连接池避免频繁创建连接
- **超时设置**: 设置合理的连接和查询超时
- **健康检查**: 定期检查连接健康状态
- **监控告警**: 监控连接池使用情况

## 7. 数据安全规则

### 7.1 敏感数据处理
```sql
-- 敏感数据加密存储
CREATE TABLE users (
    id UUID PRIMARY KEY,
    email TEXT NOT NULL,
    password_hash TEXT NOT NULL,  -- 存储哈希值，不存储明文
    encrypted_data BYTEA          -- 加密的敏感数据
);
```

### 7.2 访问控制
```sql
-- 数据库用户权限最小化
GRANT SELECT, INSERT, UPDATE, DELETE ON users TO app_user;
REVOKE ALL ON sensitive_table FROM app_user;

-- 行级安全策略
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
CREATE POLICY user_policy ON users FOR ALL TO app_user USING (user_id = current_user_id());
```

## 8. 备份和恢复规则

### 8.1 备份策略
```bash
# 定期全量备份
pg_dump -h localhost -U postgres -d database_name > backup_$(date +%Y%m%d).sql

# 增量备份 (WAL归档)
archive_command = 'cp %p /backup/wal/%f'
```

### 8.2 恢复测试
- **定期测试**: 每月测试备份恢复流程
- **恢复时间**: 记录和优化恢复时间目标(RTO)
- **数据完整性**: 验证恢复后的数据完整性
- **文档记录**: 维护详细的恢复操作文档

## 9. 监控和告警

### 9.1 关键指标
```sql
-- 监控慢查询
SELECT query, mean_time, calls 
FROM pg_stat_statements 
WHERE mean_time > 1000 
ORDER BY mean_time DESC;

-- 监控连接数
SELECT count(*) FROM pg_stat_activity;

-- 监控锁等待
SELECT * FROM pg_locks WHERE NOT granted;
```

### 9.2 告警规则
- **连接数告警**: 连接数超过80%时告警
- **慢查询告警**: 查询时间超过5秒时告警
- **磁盘空间告警**: 磁盘使用率超过85%时告警
- **复制延迟告警**: 主从延迟超过10秒时告警

## 10. 开发环境规则

### 10.1 本地开发
```yaml
# docker-compose.yml
version: '3.8'
services:
  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: resumix_dev
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
```

### 10.2 测试数据
- **种子数据**: 提供一致的测试种子数据
- **数据清理**: 测试后自动清理测试数据
- **隔离性**: 确保测试之间的数据隔离
- **真实性**: 测试数据应该接近生产数据特征

## 11. 违规检查清单

### 11.1 设计阶段检查
- [ ] 表名和字段名符合命名规范
- [ ] 主键使用UUID而非自增ID
- [ ] 外键关系正确定义
- [ ] 索引策略合理，无过度索引
- [ ] 数据类型选择恰当

### 11.2 实现阶段检查
- [ ] 迁移脚本可回滚
- [ ] 查询使用了合适的索引
- [ ] 事务边界明确定义
- [ ] 敏感数据正确处理
- [ ] 连接池配置合理

### 11.3 部署阶段检查
- [ ] 备份策略已实施
- [ ] 监控告警已配置
- [ ] 权限控制已设置
- [ ] 性能基准已建立
