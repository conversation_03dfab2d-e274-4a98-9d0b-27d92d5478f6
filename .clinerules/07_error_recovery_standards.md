# Error Recovery and Rollback Standards (错误恢复和回滚标准) v1.0

**目的**: 定义系统性的错误恢复策略、回滚机制和失败状态诊断流程，确保在出现问题时能够快速、安全地恢复到稳定状态。

## 1. 错误分类和恢复策略

### 1.1. 错误严重程度分级

#### **Critical Errors (严重错误)**
**定义**: 导致系统无法继续工作或数据损坏的错误
**示例**:
- 文件系统损坏或无法访问
- 关键配置文件丢失或损坏
- 数据库连接完全失败
- 内存不足导致系统崩溃

**恢复策略**:
```
1. 立即停止当前操作
2. 保存当前状态快照
3. 回滚到最近的稳定检查点
4. 执行完整性验证
5. 报告错误详情和恢复状态
```

#### **Major Errors (重要错误)**
**定义**: 影响主要功能但不导致系统崩溃的错误
**示例**:
- 工具调用失败
- 网络连接间歇性问题
- 部分文件操作失败
- 外部服务暂时不可用

**恢复策略**:
```
1. 重试机制 (最多3次)
2. 降级服务 (使用备用方案)
3. 跳过非关键操作
4. 记录错误但继续执行
5. 在适当时机重新尝试
```

#### **Minor Errors (一般错误)**
**定义**: 不影响核心功能的小问题
**示例**:
- 格式化问题
- 非关键文件访问失败
- 可选功能不可用
- 性能轻微下降

**恢复策略**:
```
1. 记录警告信息
2. 使用默认值或备用方案
3. 继续正常执行
4. 在后台尝试修复
```

### 1.2. 错误检测机制

#### **主动错误检测**
```
ERROR_DETECTION_TRIGGERS:
1. 工具调用返回错误状态
2. 文件操作异常
3. 验证检查失败
4. 超时或资源不足
5. 用户报告问题
6. 自动健康检查失败
```

#### **被动错误检测**
```
PASSIVE_ERROR_INDICATORS:
1. 性能显著下降
2. 输出质量异常
3. 用户满意度下降
4. 重复性问题出现
5. 资源使用异常
```

## 2. 检查点和回滚机制

### 2.1. 检查点创建标准

#### **强制检查点 (Mandatory Checkpoints)**
**触发条件**:
- Phase Gate 完成时
- 重大架构决策后
- 大量文件修改前
- 关键配置变更前
- 用户明确要求时

**检查点内容**:
```
CHECKPOINT_CONTENT:
1. 所有项目文件的状态快照
2. 当前工作进度记录
3. 配置和设置信息
4. 错误日志和警告记录
5. 性能指标数据
6. 用户交互历史
```

#### **自动检查点 (Automatic Checkpoints)**
**触发条件**:
- 每完成10个重要操作
- 每工作30分钟
- 检测到潜在风险操作前
- 系统资源使用率超过80%

### 2.2. 回滚执行流程

#### **回滚决策矩阵**
```
回滚条件评估:
- 错误严重程度: Critical/Major/Minor
- 影响范围: System/Module/Component/Function
- 修复复杂度: High/Medium/Low
- 时间成本: >2h/30min-2h/<30min

回滚决策:
- Critical + System: 立即回滚
- Major + Module + High: 回滚
- Major + Component + Medium: 尝试修复，失败则回滚
- Minor: 记录但不回滚
```

#### **回滚执行步骤**
```
ROLLBACK_EXECUTION_PROCESS:
1. 验证回滚目标检查点的完整性
2. 创建当前状态的紧急备份
3. 停止所有正在进行的操作
4. 恢复文件系统到检查点状态
5. 恢复配置和设置
6. 验证回滚后的系统完整性
7. 重新初始化必要的服务
8. 执行基本功能测试
9. 记录回滚操作和原因
10. 向用户报告回滚结果
```

## 3. 失败状态诊断

### 3.1. 诊断信息收集

#### **系统状态诊断**
```
SYSTEM_DIAGNOSTICS:
1. 文件系统状态检查
   - 磁盘空间使用率
   - 文件权限和访问性
   - 文件完整性验证
   
2. 内存和性能状态
   - 内存使用情况
   - CPU 使用率
   - 网络连接状态
   
3. 工具和服务状态
   - 各工具的可用性
   - 外部服务连接状态
   - API 调用成功率
```

#### **项目状态诊断**
```
PROJECT_DIAGNOSTICS:
1. 文档完整性检查
   - 所有必需文档是否存在
   - 文档结构是否完整
   - 文档间一致性验证
   
2. 逻辑一致性检查
   - 接口定义一致性
   - 数据模型一致性
   - 业务规则一致性
   
3. 实现进度检查
   - 已完成任务状态
   - 当前工作进度
   - 剩余工作评估
```

### 3.2. 根因分析方法

#### **5-Why 分析法**
```
ROOT_CAUSE_ANALYSIS_TEMPLATE:
问题描述: [具体的错误或失败现象]

Why 1: 为什么会发生这个问题？
答案: [直接原因]

Why 2: 为什么会出现这个直接原因？
答案: [更深层的原因]

Why 3: 为什么会出现这个更深层的原因？
答案: [系统性原因]

Why 4: 为什么系统会有这个问题？
答案: [设计或流程问题]

Why 5: 为什么设计或流程会有这个问题？
答案: [根本原因]

根本原因: [最终识别的根本原因]
预防措施: [防止问题再次发生的措施]
```

#### **系统性问题诊断流程 (基于2025-07-02实践)**
```
SYSTEMATIC_DIAGNOSIS_PROCESS:
1. 问题现象收集
   - 收集错误日志和用户报告
   - 记录问题发生的具体环境和条件
   - 确定问题的影响范围和严重程度

2. 初步假设形成
   - 基于现象提出可能的原因假设
   - 按照可能性和影响程度排序假设
   - 制定验证每个假设的测试方法

3. 逐步验证测试
   - 从最可能的假设开始验证
   - 使用最小化测试方法避免引入新问题
   - 记录每次测试的结果和观察

4. 根因确认
   - 通过重现问题确认根本原因
   - 验证修复方案的有效性
   - 确保修复不会引入新的问题

5. 解决方案实施
   - 实施经过验证的修复方案
   - 进行全面的回归测试
   - 监控系统稳定性和性能指标
```

#### **数据库兼容性问题诊断案例 (2025-07-02)**
```
CASE_STUDY_DATABASE_COMPATIBILITY:
问题现象:
- Worker成功处理LLM请求但无法更新数据库
- UPDATE操作返回"rows affected: 0"
- 任务状态始终保持PROCESSING

诊断过程:
1. 初步假设: 数据库触发器冲突
   验证方法: 移除手动updated_at参数
   结果: 问题依然存在

2. 深入调查: 检查SQL执行日志
   发现: 使用PostgreSQL语法($1, $2)但连接SQLite数据库
   验证: 检查database.go中的驱动检测逻辑

3. 根因确认: 参数占位符不兼容
   PostgreSQL: $1, $2, $3, $4
   SQLite: ?, ?, ?, ?

解决方案:
- 转换参数占位符语法为SQLite兼容格式
- 调整参数传递顺序匹配占位符位置
- 验证UPDATE操作返回正确的affected rows

预防措施:
- 在数据库规范中明确多数据库兼容性要求
- 添加数据库兼容性集成测试
- 建立参数占位符使用规范
```

#### **Worker错误处理优化案例 (2025-07-02)**
```
CASE_STUDY_WORKER_ERROR_HANDLING:
问题现象:
- Worker在空队列时记录大量错误日志
- "redis: nil" 错误被当作系统故障处理
- 正常业务状态被误报为错误条件

诊断过程:
1. 错误分类分析
   - 区分系统错误 vs 业务条件
   - 识别Redis "nil"响应的真实含义
   - 分析错误处理逻辑的设计缺陷

2. 错误处理模式检查
   - 检查Go错误类型检测方法
   - 评估字符串匹配 vs 类型安全检测
   - 分析日志记录策略的合理性

解决方案:
- 实现类型安全的错误检测(errors.As())
- 将空队列条件从错误改为正常业务状态
- 优化日志记录策略，区分错误级别

预防措施:
- 在架构规范中明确错误分类原则
- 建立Worker错误处理最佳实践
- 添加错误处理模式的代码审查标准
```

## 4. 预防性措施

### 4.1. 风险预警机制

#### **风险指标监控**
```
RISK_INDICATORS:
1. 性能指标异常
   - 响应时间超过阈值
   - 资源使用率过高
   - 错误率上升
   
2. 质量指标下降
   - 验证失败率增加
   - 用户反馈负面
   - 重复性问题出现
   
3. 复杂度指标超标
   - 单个操作涉及文件过多
   - 依赖关系过于复杂
   - 修改频率过高
```

#### **预警响应机制**
```
WARNING_RESPONSE_ACTIONS:
1. 黄色预警 (轻微风险):
   - 增加监控频率
   - 记录详细日志
   - 准备应急方案
   
2. 橙色预警 (中等风险):
   - 创建紧急检查点
   - 暂停非关键操作
   - 通知用户潜在风险
   
3. 红色预警 (高风险):
   - 立即停止当前操作
   - 激活应急响应流程
   - 准备回滚方案
```

### 4.2. 健壮性设计原则

#### **防御性编程**
```
DEFENSIVE_PROGRAMMING_PRINCIPLES:
1. 输入验证
   - 所有外部输入都必须验证
   - 使用白名单而非黑名单
   - 提供有意义的错误信息
   
2. 错误处理
   - 每个操作都有错误处理
   - 优雅降级而非崩溃
   - 记录足够的诊断信息
   
3. 资源管理
   - 及时释放资源
   - 设置合理的超时
   - 监控资源使用情况
```

## 5. 恢复验证标准

### 5.1. 恢复完整性验证

#### **验证检查清单**
```
RECOVERY_VERIFICATION_CHECKLIST:
1. 文件系统完整性
   [ ] 所有必需文件存在
   [ ] 文件内容完整且可读
   [ ] 文件权限正确设置
   
2. 配置完整性
   [ ] 所有配置文件有效
   [ ] 配置参数在合理范围内
   [ ] 依赖关系正确配置
   
3. 功能完整性
   [ ] 核心功能正常工作
   [ ] 工具调用成功
   [ ] 外部服务连接正常
   
4. 数据完整性
   [ ] 数据结构正确
   [ ] 数据关系一致
   [ ] 数据验证通过
```

### 5.2. 恢复质量评估

#### **恢复成功标准**
```
RECOVERY_SUCCESS_CRITERIA:
1. 功能恢复率 ≥ 95%
2. 数据完整性 = 100%
3. 性能恢复率 ≥ 90%
4. 用户体验影响 ≤ 最小化
5. 恢复时间 ≤ 预期时间
```

## 6. 应急响应流程

### 6.1. 应急响应团队角色

#### **响应角色定义**
```
EMERGENCY_RESPONSE_ROLES:
1. 主响应者 (Primary Responder)
   - 评估问题严重程度
   - 决定响应策略
   - 协调恢复行动
   
2. 技术专家 (Technical Expert)
   - 提供技术诊断
   - 执行恢复操作
   - 验证恢复结果
   
3. 沟通协调者 (Communication Coordinator)
   - 与用户沟通
   - 更新状态信息
   - 记录响应过程
```

### 6.2. 应急响应时间线

#### **响应时间要求**
```
RESPONSE_TIME_REQUIREMENTS:
Critical Errors:
- 检测到问题: 立即 (0分钟)
- 初始响应: 5分钟内
- 问题评估: 15分钟内
- 恢复行动: 30分钟内
- 完全恢复: 2小时内

Major Errors:
- 检测到问题: 5分钟内
- 初始响应: 15分钟内
- 问题评估: 30分钟内
- 恢复行动: 1小时内
- 完全恢复: 4小时内

Minor Errors:
- 检测到问题: 15分钟内
- 初始响应: 1小时内
- 问题评估: 2小时内
- 恢复行动: 4小时内
- 完全恢复: 1工作日内
```

---

**核心原则**: 错误恢复应该是快速的、安全的、可验证的。通过系统性的错误分类、检查点管理和恢复验证，确保在任何情况下都能维持系统的稳定性和可靠性。
