#!/usr/bin/env python3
"""
Example usage of PromptManager.

This script demonstrates how to use the Prompt<PERSON>anager to manage AI agent prompts.
"""

from prompt_manager import Prompt<PERSON>anager, PromptConfigurationError, AgentNotFoundError


def main():
    """Demonstrate PromptManager usage."""
    
    print("=== PromptManager Example Usage ===\n")
    
    try:
        # Initialize PromptManager
        print("1. Initializing PromptManager...")
        pm = PromptManager("./example_prompt_store")
        print(f"   Initialized with {len(pm.list_agents())} existing agents\n")
        
        # Register a new agent
        print("2. Registering a new agent 'writer'...")
        system_message = """You are a professional writer assistant. 
Your task is to help users create high-quality written content.

Writing Style Guide:
{writing_style_guide}

Please follow these guidelines when assisting users."""
        
        user_message = """Please help me write: {user_request}

Consider the writing style guide and provide a well-structured response."""
        
        writing_style_guide = """
1. Use clear and concise language
2. Maintain a professional tone
3. Structure content with proper headings
4. Include examples when appropriate
5. Proofread for grammar and spelling
"""
        
        pm.register_new_agent(
            agent_name="writer",
            system_message=system_message,
            user_message=user_message,
            writing_style_guide=writing_style_guide
        )
        print("   Agent 'writer' registered successfully\n")
        
        # List all agents
        print("3. Listing all agents...")
        agents = pm.list_agents()
        print(f"   Available agents: {agents}\n")
        
        # Get agent information
        print("4. Getting agent information...")
        info = pm.get_agent_info("writer")
        print(f"   Agent info: {info}\n")
        
        # Get prompts for the agent
        print("5. Getting prompts for 'writer' agent...")
        prompts = pm.get_prompts("writer")
        print(f"   System template variables: {prompts['system'].input_variables}")
        print(f"   User template variables: {prompts['user'].input_variables}\n")
        
        # Format a sample prompt
        print("6. Formatting sample prompts...")
        system_prompt = prompts['system'].format()
        user_prompt = prompts['user'].format(user_request="a blog post about AI")
        
        print("   System prompt:")
        print(f"   {system_prompt[:200]}...\n")
        
        print("   User prompt:")
        print(f"   {user_prompt}\n")
        
        # Register another agent with no variables
        print("7. Registering simple agent 'greeter'...")
        pm.register_new_agent(
            agent_name="greeter",
            system_message="You are a friendly greeting assistant.",
            user_message="Please greet the user: {name}"
        )
        print("   Agent 'greeter' registered successfully\n")
        
        # Test the greeter
        print("8. Testing greeter agent...")
        greeter_prompts = pm.get_prompts("greeter")
        greeting = greeter_prompts['user'].format(name="Alice")
        print(f"   Greeting: {greeting}\n")
        
        # Reload an agent
        print("9. Reloading 'writer' agent...")
        pm.reload_agent("writer")
        print("   Agent 'writer' reloaded successfully\n")
        
        # Clean up - delete test agents
        print("10. Cleaning up test agents...")
        pm.delete_agent("writer")
        pm.delete_agent("greeter")
        print("    Test agents deleted successfully\n")
        
        print("=== Example completed successfully! ===")
        
    except PromptConfigurationError as e:
        print(f"Configuration error: {e}")
        if hasattr(e, 'context'):
            print(f"Context: {e.context}")
    
    except AgentNotFoundError as e:
        print(f"Agent not found: {e}")
        if hasattr(e, 'context'):
            print(f"Context: {e.context}")
    
    except Exception as e:
        print(f"Unexpected error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
