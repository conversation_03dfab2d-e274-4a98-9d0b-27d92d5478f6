#!/usr/bin/env python3
"""
Create demo agents to show PromptManager file structure.

This script creates several example agents to demonstrate the file organization.
"""

from prompt_manager import Prompt<PERSON>anager


def get_system_message():

    
    # Initialize PromptManager
    pm = PromptManager("./prompt_store")
    
    # Create a writer agent with style guide
    system_msg_prompt_template = pm.get_prompts('coder')['system']
    var = system_msg_prompt_template.partial_variables
    print(system_msg_prompt_template.invoke(system_msg_prompt_template.partial_variables).text)


if __name__ == "__main__":
    get_system_message()
