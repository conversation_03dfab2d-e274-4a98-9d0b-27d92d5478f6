from google import genai
from google.genai import types
import asyncio
import json
import pandas as pd
import os
from datetime import datetime

def save_to_csv(data_dict, append_mode=False):
    """
    Save the dictionary data to two CSV files:
    1. keyword_analysis.csv - contains keyword and analysis attributes
    2. keyword_results.csv - contains keyword and top_5_results attributes
    
    Args:
        data_dict: Dictionary containing the API response data
        append_mode: If True, append to existing files; if False, overwrite
    """
    if not data_dict:
        print("No data to save")
        return
    
    keyword = data_dict.get('keyword', '')
    analysis = data_dict.get('analysis', {})
    top_5_results = data_dict.get('top_5_results', [])
    
    # Add timestamp for tracking
    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    
    # Create analysis CSV
    analysis_data = {
        'keyword': [keyword],
        'user_search_intent': [analysis.get('user_search_intent', '')],
        'existing_content_formats': ['; '.join(analysis.get('existing_content_formats', []))],
        'content_gaps': ['; '.join(analysis.get('content_gaps', []))],
        'timestamp': [timestamp]
    }
    
    analysis_df = pd.DataFrame(analysis_data)
    
    # Save analysis data
    analysis_file = 'keyword_analysis.csv'
    if append_mode and os.path.exists(analysis_file):
        analysis_df.to_csv(analysis_file, mode='a', header=False, index=False, encoding='utf-8')
        print(f"Analysis data appended to {analysis_file}")
    else:
        analysis_df.to_csv(analysis_file, index=False, encoding='utf-8')
        print(f"Analysis data saved to {analysis_file}")
    
    # Create results CSV
    results_data = []
    for result in top_5_results:
        result_row = {
            'keyword': keyword,
            'rank': result.get('rank', ''),
            'title': result.get('title', ''),
            'url': result.get('url', ''),
            'summary': result.get('summary', ''),
            'timestamp': timestamp
        }
        results_data.append(result_row)
    
    if results_data:
        results_df = pd.DataFrame(results_data)
        results_file = 'keyword_results.csv'
        
        if append_mode and os.path.exists(results_file):
            results_df.to_csv(results_file, mode='a', header=False, index=False, encoding='utf-8')
            print(f"Results data appended to {results_file}")
        else:
            results_df.to_csv(results_file, index=False, encoding='utf-8')
            print(f"Results data saved to {results_file}")
    else:
        print("No results data to save")

async def get_browser_content(search_content: str, api_key: str = 'AIzaSyBxVGph11TTLKKfGBXH3GVPIXriYaroV9E') -> dict:
    """
    Get browser content analysis for a given search term
    
    Args:
        search_content: The keyword to search for
        api_key: Google API key
        
    Returns:
        Dictionary containing the analysis results or None if failed
    """
    try:
        # Configure the client
        client = genai.Client(api_key=api_key)

        # Define the grounding tool
        grounding_tool = types.Tool(
            google_search=types.GoogleSearch()
        )

        url_tool = types.Tool(url_context=types.UrlContext())

        # Configure generation settings
        config = types.GenerateContentConfig(
            tools=[grounding_tool, url_tool]
        )

        print(f"Analyzing keyword: {search_content}")
        print("Making API request... (this may take 1-2 minutes)")

        # Make the request
        response = client.models.generate_content(
            model="gemini-2.5-flash",
            contents=f'''
            Analyze the top 5 Google search results for the keyword `{search_content}`.

    **Objective:**
    Identify the user search intent, content gaps, and existing content formats for the given keyword.

    **Process:**
    1.  Perform a Google search for `{search_content}` to retrieve the top 5 search results.
    2.  For each of the top 5 URLs, analyze the content.
    3.  Based on the analysis of all results, provide a summary that includes:
        *   **User Search Intent:** Determine the primary reason a user is searching for this keyword (e.g., informational, navigational, transactional, commercial investigation).
        *   **Existing Content Formats:** Identify the common formats of the top-ranking content (e.g., blog posts, videos, product pages, reviews, guides, news articles).
        *   **Content Gaps:** Pinpoint relevant subtopics, user questions, or angles that are not well-covered by the existing top results.

    **Output:**
    Return the findings in a structured JSON format as follows:

    {{
      "keyword": "{search_content}",
      "analysis": {{
        "user_search_intent": "...",
        "existing_content_formats": [
          "...",
          "..."
        ],
        "content_gaps": [
          "...",
          "..."
        ]
      }},
      "top_5_results": [
        {{
          "rank": 1,
          "title": "...",
          "url": "...",
          "summary": "..."
        }},
        {{
          "rank": 2,
          "title": "...",
          "url": "...",
          "summary": "..."
        }},
        {{
          "rank": 3,
          "title": "...",
          "url": "...",
          "summary": "..."
        }},
        {{
          "rank": 4,
          "title": "...",
          "url": "...",
          "summary": "..."
        }},
        {{
          "rank": 5,
          "title": "...",
          "url": "...",
          "summary": "..."
        }}
      ]
    }}

            ''',
            config=config,
        )

        # Parse the response
        cleared_text = response.text.strip('\n').strip('```').split('json')[1]
        dic = json.loads(cleared_text)
        
        print("✓ API request successful")
        print(f"✓ Received data for keyword: {dic.get('keyword', 'Unknown')}")
        
        return dic
        
    except json.JSONDecodeError as e:
        print(f"✗ Error parsing JSON response: {e}")
        print(f"Raw response: {response.text}")
        return None
    except Exception as e:
        print(f"✗ Error during API request: {e}")
        return None

async def process_multiple_keywords(keywords_list, api_key=None):
    """
    Process multiple keywords and save results to CSV
    
    Args:
        keywords_list: List of keywords to process
        api_key: Optional API key override
    """
    print(f"Processing {len(keywords_list)} keywords...")
    print("="*50)
    
    for i, keyword in enumerate(keywords_list, 1):
        print(f"\n[{i}/{len(keywords_list)}] Processing: {keyword}")
        
        # Get data for this keyword
        if api_key:
            data = await get_browser_content(keyword, api_key)
        else:
            data = await get_browser_content(keyword)
        
        if data:
            # Save to CSV (append mode for multiple keywords)
            append_mode = i > 1  # First keyword creates new files, rest append
            save_to_csv(data, append_mode=append_mode)
            print(f"✓ Data saved for keyword: {keyword}")
        else:
            print(f"✗ Failed to get data for keyword: {keyword}")
        
        print("-" * 30)
    
    print(f"\n✓ Completed processing {len(keywords_list)} keywords")
    print("Check keyword_analysis.csv and keyword_results.csv for results")

# Example usage
if __name__ == "__main__":
    # Single keyword example
    single_keyword = "COVID-19 Antigen Rapid Test"
    
    print("=== Single Keyword Processing ===")
    result = asyncio.run(get_browser_content(single_keyword))
    if result:
        save_to_csv(result)
    
    # Multiple keywords example (uncomment to use)
    # keywords = [
    #     "AI Machine Learning",
    #     "Python Programming",
    #     "Data Science Tools"
    # ]
    # 
    # print("\n=== Multiple Keywords Processing ===")
    # asyncio.run(process_multiple_keywords(keywords))
