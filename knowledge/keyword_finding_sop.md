### **ACE-PRO 关键词战略生成逻辑 V3.1：自动化行业图谱构建与机会映射模型**

**核心思想：**
本逻辑的核心是实现一个**自举（Bootstrapping）**的、**由内而外再由外而内**的闭环系统。我们首先利用我们唯一的确知信息——**现有的产品列表**——作为“知识种子”，通过大语言模型（LLM）的归纳能力，反向工程出整个IVD行业的宏观知识图谱。然后，我们用这个宏观图谱作为“战略罗盘”，去全面探索和生成覆盖整个行业的关键词宇宙。最后，我们将这些外部的、代表用户需求的关键词，与我们内部的产品进行**双向映射**，从而不仅为现有产品找到优化方向，更能发现全新的内容创作和产品战略机会。

**最终的执行流程分为两个核心阶段：**

---

### **第一阶段：行业图谱的自动化构建 (Automated Industry Map Construction)**

**目标：** 在没有任何人类专家输入的情况下，仅从 `products.xlsx` 出发，自动化地构建出整个IVD行业的核心概念图谱。这一步由一个**全新的、前置的AI分析模块**完成。

1.  **输入：** `products.xlsx` 文件，即该项目的tabular/backed.csv。

2.  **动作：**
    *   **a. 产品信息聚合：** 脚本读取并汇总`products.xlsx`中所有的`product_name`和`category_a`。
    *   **b. LLM归纳推理 (AI扮演战略分析师):** 脚本将聚合的产品信息，通过一个精心设计的Prompt发送给一个强大的LLM（如GPT-4）。
        *   **Prompt核心指令：** “你是一名顶级的IVD行业战略分析师。基于以下我司的产品列表，请你进行归纳和演绎，输出一个覆盖整个行业的、全面的核心概念图谱。你的输出必须超越我提供的列表，反映出整个行业的结构。”
    *   **c. 概念图谱生成：** LLM会返回一个结构化的JSON对象，其中包含：
        *   **`disease_areas` (疾病领域):** 如"传染病", "肿瘤标志物", "心脏标志物"等。
        *   **`technologies` (核心技术):** 如"免疫层析法(Colloidal Gold)", "ELISA", "qPCR"等。
        *   **`applications` (应用场景):** 如"即时检测(POCT)", "临床诊断", "大规模筛查"等。
        *   **`compliance_and_quality` (法规与质量):** 如"CE认证", "FDA批准", "ISO 13485"等。

3.  **产出：** 一个由AI自动生成的、代表IVD行业宏观结构的**“行业概念图谱”**（一个Python字典）。这是后续所有工作的基础。

---

### **第二阶段：基于图谱的关键词宇宙生成 (Map-Driven Keyword Universe Generation)**

**目标：** 利用第一阶段生成的行业图谱，系统性地、无死角地生成覆盖所有商业意图的潜在关键词。

1.  **输入：** 第一阶段生成的“行业概念图谱”。

2.  **动作 (继承并优化V2.0的矩阵逻辑):**
    *   **a. 概念遍历：** 脚本会遍历图谱中的**每一个概念**（例如，`disease_areas`中的`Syphilis`，或者`technologies`中的`qPCR`）。
    *   **b. 语义维度定义：** 脚本内置了多个“商业语义维度”，包括：
        *   `产品描述符`: `test kit`, `rapid test`...
        *   `商业模式`: `wholesale`, `bulk`...
        *   `供应商身份`: `supplier`, `manufacturer`...
        *   `购买意图`: `price`, `cost`, `buy`...
    *   **c. 矩阵式交叉组合：** 对于图谱中的每一个概念，脚本都会将其与上述所有商业语义维度进行**多层次的、系统的交叉组合**。这将生成海量的、意图明确的种子关键词。
        *   **简单组合示例：** `qPCR test kit`
        *   **复杂组合示例：** `wholesale qPCR diagnostic kit price manufacturer`
    *   **d. API调用与数据获取：** 将组合出的种子关键词分批次、有策略地（每次取样或截断）发送给Google Ads API，获取每个潜在关键词的**真实市场数据**（平均月搜索量、竞争度、建议出价等）。

3.  **产出：** 一个极其庞大的、包含了真实市场数据的**“行业关键词数据库”**。



