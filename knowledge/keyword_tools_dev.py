# -*- coding: utf-8 -*-
"""
一个使用 Google Ads API 获取关键词建议的工具脚本。

运行此脚本前，请确保：
1. 已安装 google-ads 库: pip install google-ads
2. 项目根目录下存在一个有效的 `google-ads.yaml` 配置文件。
3. 下方的 TEST_CUSTOMER_ID 已被替换为一个有效的测试客户账户 ID。
"""

from google.ads.googleads.client import GoogleAdsClient
from google.ads.googleads.errors import GoogleAdsException

# ！！！在这里填入你【全新的、有效的】测试客户账户ID！！！
#    （您需要重新创建一个状态为“有效”的测试客户账户，并为其设置好结算信息，然后把ID填在这里）
TEST_CUSTOMER_ID = "8631619266" # 这是一个示例ID，请务必替换为您自己的


def get_keyword_ideas(
    customer_id: str,
    keyword_texts: list[str],
    location_ids: list[str] = None,
    language_id: str = "1000", # 1000 for English
):
    """
    获取关键词建议列表。

    :param customer_id: 【必需】要操作的目标客户ID。
    :param keyword_texts: 【必需】用于生成建议的种子关键词列表。
    :param location_ids: 可选，地理位置ID列表。例如 "2840" 代表中国。
    :param language_id: 可选，语言ID。例如 "1000" 代表英语，"1017" 代表简体中文。
    :return: 包含关键词建议信息的字典列表，或在失败时返回空列表。
    """
    if not customer_id:
        print("错误：必须提供 customer_id。")
        return []

    try:
        # 加载配置 (会自动寻找 "google-ads.yaml" 文件)
        client = GoogleAdsClient.load_from_storage("google-ads.yaml")
        keyword_plan_idea_service = client.get_service("KeywordPlanIdeaService")

        # --- 构建 API 请求 ---
        request = client.get_type("GenerateKeywordIdeasRequest")
        request.customer_id = customer_id
        
        # 设置语言
        request.language = f"languageConstants/{language_id}"
        
        # 设置地理位置 (如果提供了)
        if location_ids:
            for loc_id in location_ids:
                request.geo_target_constants.append(f"geoTargetConstants/{loc_id}")

        # 设置种子关键词
        request.keyword_seed.keywords.extend(keyword_texts)

        # --- 调用 API ---
        response = keyword_plan_idea_service.generate_keyword_ideas(request=request)

        # --- 解析并格式化结果 ---
        result = []
        for idea in response:
            metrics = idea.keyword_idea_metrics
            result.append(
                {
                    "keyword": idea.text,
                    "avg_monthly_searches": metrics.avg_monthly_searches,
                    # 使用 .name 属性获取枚举名称，这是更简洁的方式
                    "competition": metrics.competition.name, 
                }
            )
        
        return result

    except GoogleAdsException as ex:
        # 打印非常详细的错误信息，便于调试
        print(
            f"Request with ID '{ex.request_id}' failed with status "
            f"'{ex.error.code().name}' and includes the following errors:"
        )
        for error in ex.failure.errors:
            print(f"\tError with message '{error.message}'.")
            if error.location:
                for field_path_element in error.location.field_path_elements:
                    print(f"\t\tOn field: {field_path_element.field_name}")
        return []
    except FileNotFoundError:
        print("e错误：找不到 `google-ads.yaml` 配置文件。")
        print("请确保该文件与您的脚本在同一目录，或指定了正确的路径。")
        return []
    except Exception as e:
        print(f"发生未知错误: {e}")
        return []


# ✅ 使用示例
if __name__ == "__main__":
    if "请在这里" in TEST_CUSTOMER_ID or "xxxx" in TEST_CUSTOMER_ID or len(TEST_CUSTOMER_ID.replace("-", "")) != 10:
        print("!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!")
        print("!!! 错误：请先重新创建一个【有效的】测试客户账户，     !!!")
        print("!!!       然后将其ID填写到代码第14行的变量中！       !!!")
        print("!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!")
    else:
        # 示例1：查询美国的SEO相关关键词
        print("--- 正在查询美国地区的SEO关键词... ---")
        keywords_en = ["seo tools", "keyword planner"]
        # Location ID "2840" is for the United States
        data_us = get_keyword_ideas(
            TEST_CUSTOMER_ID, keywords_en, location_ids=["2840"], language_id="1000"
        )
        
        if data_us:
            print("🎉🎉🎉 成功获取【美国地区】关键词建议：🎉🎉🎉")
            for row in data_us[:5]: # 只打印前5条结果
                print(row)
        else:
            print("❌ 未能获取到美国地区的关键词建议。请检查上面的错误信息。")

        print("\n" + "="*50 + "\n")

        # 示例2：查询中国的“跨境电商”相关关键词
        print("--- 正在查询中国地区的“跨境电商”关键词... ---")
        keywords_cn = ["跨境电商", "外贸推广"]
        # Location ID "2156" is for China, Language "1017" is for Simplified Chinese
        data_cn = get_keyword_ideas(
            TEST_CUSTOMER_ID, keywords_cn, location_ids=["2156"], language_id="1017"
        )

        if data_cn:
            print("🎉🎉🎉 成功获取【中国地区】关键词建议：🎉🎉🎉")
            for row in data_cn[:5]: # 只打印前5条结果
                print(row)
        else:
            print("❌ 未能获取到中国地区的关键词建议。请检查上面的错误信息。")