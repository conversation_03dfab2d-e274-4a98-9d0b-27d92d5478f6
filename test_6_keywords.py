from google import genai
from google.genai import types
import asyncio
import json
import pandas as pd
import os
from datetime import datetime

def save_to_csv(data_dict, append_mode=False):
    """
    Save the dictionary data to two CSV files:
    1. keyword_analysis.csv - contains keyword and analysis attributes
    2. keyword_results.csv - contains keyword and top_5_results attributes
    
    Args:
        data_dict: Dictionary containing the API response data
        append_mode: If True, append to existing files; if False, overwrite
    """
    if not data_dict:
        print("No data to save")
        return
    
    keyword = data_dict.get('keyword', '')
    analysis = data_dict.get('analysis', {})
    top_5_results = data_dict.get('top_5_results', [])
    
    # Add timestamp for tracking
    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    
    # Create analysis CSV
    analysis_data = {
        'keyword': [keyword],
        'user_search_intent': [analysis.get('user_search_intent', '')],
        'existing_content_formats': ['; '.join(analysis.get('existing_content_formats', []))],
        'content_gaps': ['; '.join(analysis.get('content_gaps', []))],
        'timestamp': [timestamp]
    }
    
    analysis_df = pd.DataFrame(analysis_data)
    
    # Save analysis data
    if append_mode and os.path.exists('test_keyword_analysis.csv'):
        analysis_df.to_csv('test_keyword_analysis.csv', mode='a', header=False, index=False, encoding='utf-8')
        print(f"Analysis data appended to test_keyword_analysis.csv")
    else:
        analysis_df.to_csv('test_keyword_analysis.csv', index=False, encoding='utf-8')
        print(f"Analysis data saved to test_keyword_analysis.csv")
    
    # Create results CSV
    results_data = []
    for result in top_5_results:
        result_row = {
            'keyword': keyword,
            'rank': result.get('rank', ''),
            'title': result.get('title', ''),
            'url': result.get('url', ''),
            'summary': result.get('summary', ''),
            'timestamp': timestamp
        }
        results_data.append(result_row)
    
    if results_data:
        results_df = pd.DataFrame(results_data)
        
        if append_mode and os.path.exists('test_keyword_results.csv'):
            results_df.to_csv('test_keyword_results.csv', mode='a', header=False, index=False, encoding='utf-8')
            print(f"Results data appended to test_keyword_results.csv")
        else:
            results_df.to_csv('test_keyword_results.csv', index=False, encoding='utf-8')
            print(f"Results data saved to test_keyword_results.csv")
    else:
        print("No results data to save")

async def get_browser_content(search_content: str) -> dict:
    """
    Get browser content analysis for a given search term
    
    Args:
        search_content: The keyword to search for
        
    Returns:
        Dictionary containing the analysis results or None if failed
    """
    try:
        # Configure the client
        client = genai.Client(api_key='AIzaSyBxVGph11TTLKKfGBXH3GVPIXriYaroV9E')

        # Define the grounding tool
        grounding_tool = types.Tool(
            google_search=types.GoogleSearch()
        )

        url_tool = types.Tool(url_context=types.UrlContext())

        # Configure generation settings
        config = types.GenerateContentConfig(
            tools=[grounding_tool, url_tool]
        )

        print(f"Analyzing keyword: {search_content}")

        # Make the request
        response = client.models.generate_content(
            model="gemini-2.5-flash",
            contents=f'''
            Analyze the top 5 Google search results for the keyword `{search_content}`.

    **Objective:**
    Identify the user search intent, content gaps, and existing content formats for the given keyword.

    **Process:**
    1.  Perform a Google search for `{search_content}` to retrieve the top 5 search results.
    2.  For each of the top 5 URLs, analyze the content.
    3.  Based on the analysis of all results, provide a summary that includes:
        *   **User Search Intent:** Determine the primary reason a user is searching for this keyword (e.g., informational, navigational, transactional, commercial investigation).
        *   **Existing Content Formats:** Identify the common formats of the top-ranking content (e.g., blog posts, videos, product pages, reviews, guides, news articles).
        *   **Content Gaps:** Pinpoint relevant subtopics, user questions, or angles that are not well-covered by the existing top results.

    **Output:**
    Return the findings in a structured JSON format as follows:

    {{
      "keyword": "{search_content}",
      "analysis": {{
        "user_search_intent": "...",
        "existing_content_formats": [
          "...",
          "..."
        ],
        "content_gaps": [
          "...",
          "..."
        ]
      }},
      "top_5_results": [
        {{
          "rank": 1,
          "title": "...",
          "url": "...",
          "summary": "..."
        }},
        {{
          "rank": 2,
          "title": "...",
          "url": "...",
          "summary": "..."
        }},
        {{
          "rank": 3,
          "title": "...",
          "url": "...",
          "summary": "..."
        }},
        {{
          "rank": 4,
          "title": "...",
          "url": "...",
          "summary": "..."
        }},
        {{
          "rank": 5,
          "title": "...",
          "url": "...",
          "summary": "..."
        }}
      ]
    }}

            ''',
            config=config,
        )

        # Parse the response
        cleared_text = response.text.strip('\n').strip('```').split('json')[1]
        dic = json.loads(cleared_text)
        
        print(f"✓ Successfully processed: {dic.get('keyword', 'Unknown')}")
        return dic
        
    except json.JSONDecodeError as e:
        print(f"✗ Error parsing JSON response: {e}")
        return None
    except Exception as e:
        print(f"✗ Error during API request: {e}")
        return None

async def test_6_keywords():
    """Test the script with 6 specific keywords"""
    keywords = [
        "Anaplasma Antibody",
        "Brucella", 
        "Campylobacter",
        "Clostridium",
        "Leptospira",
        "Paratyphoid"
    ]
    
    print(f"Testing {len(keywords)} keywords...")
    print("="*60)
    
    all_results = []
    
    for i, keyword in enumerate(keywords, 1):
        print(f"\n[{i}/{len(keywords)}] Processing: {keyword}")
        print("-" * 40)
        
        try:
            result = await get_browser_content(keyword)
            if result:
                # Save to CSV (append mode after first keyword)
                append_mode = i > 1
                save_to_csv(result, append_mode=append_mode)
                all_results.append(result)
                print(f"✓ Successfully saved: {keyword}")
            else:
                print(f"✗ Failed to process: {keyword}")
        except Exception as e:
            print(f"✗ Error processing {keyword}: {e}")
        
        # Add a small delay between requests
        if i < len(keywords):
            print("Waiting 3 seconds before next request...")
            await asyncio.sleep(3)
    
    print("\n" + "="*60)
    print(f"✓ Completed! Successfully processed {len(all_results)}/{len(keywords)} keywords")
    print("Check test_keyword_analysis.csv and test_keyword_results.csv for results")
    
    # Display summary
    if all_results:
        print(f"\n📊 Summary:")
        for result in all_results:
            keyword = result.get('keyword', 'Unknown')
            intent = result.get('analysis', {}).get('user_search_intent', '')[:50] + '...'
            results_count = len(result.get('top_5_results', []))
            print(f"- {keyword}: {results_count} results, Intent: {intent}")
    
    return all_results

# Run the test
if __name__ == "__main__":
    asyncio.run(test_6_keywords())
