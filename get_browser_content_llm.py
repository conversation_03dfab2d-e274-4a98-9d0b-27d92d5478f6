from google import genai
from google.genai import types
import asyncio
import json
import pandas as pd
import os
import pandas as pd
import os

def save_to_csv(data_dict, append_mode=False):
    """
    Save the dictionary data to two CSV files:
    1. keyword_analysis.csv - contains keyword and analysis attributes
    2. keyword_results.csv - contains keyword and top_5_results attributes

    Args:
        data_dict: Dictionary containing the API response data
        append_mode: If True, append to existing files; if False, overwrite
    """
    if not data_dict:
        print("No data to save")
        return

    keyword = data_dict.get('keyword', '')
    analysis = data_dict.get('analysis', {})
    top_5_results = data_dict.get('top_5_results', [])

    # Create analysis CSV
    analysis_data = {
        'keyword': [keyword],
        'user_search_intent': [analysis.get('user_search_intent', '')],
        'existing_content_formats': ['; '.join(analysis.get('existing_content_formats', []))],
        'content_gaps': ['; '.join(analysis.get('content_gaps', []))]
    }

    analysis_df = pd.DataFrame(analysis_data)

    # Save analysis data
    if append_mode and os.path.exists('keyword_analysis.csv'):
        analysis_df.to_csv('keyword_analysis.csv', mode='a', header=False, index=False, encoding='utf-8')
        print("Analysis data appended to keyword_analysis.csv")
    else:
        analysis_df.to_csv('keyword_analysis.csv', index=False, encoding='utf-8')
        print("Analysis data saved to keyword_analysis.csv")

    # Create results CSV
    results_data = []
    for result in top_5_results:
        result_row = {
            'keyword': keyword,
            'rank': result.get('rank', ''),
            'title': result.get('title', ''),
            'url': result.get('url', ''),
            'summary': result.get('summary', '')
        }
        results_data.append(result_row)

    if results_data:
        results_df = pd.DataFrame(results_data)

        if append_mode and os.path.exists('keyword_results.csv'):
            results_df.to_csv('keyword_results.csv', mode='a', header=False, index=False, encoding='utf-8')
            print("Results data appended to keyword_results.csv")
        else:
            results_df.to_csv('keyword_results.csv', index=False, encoding='utf-8')
            print("Results data saved to keyword_results.csv")
    else:
        print("No results data to save")

async def get_browser_content(search_content : str) -> str:
    # Configure the client
    client = genai.Client(api_key='AIzaSyBxVGph11TTLKKfGBXH3GVPIXriYaroV9E')

    # Define the grounding tool
    grounding_tool = types.Tool(
        google_search=types.GoogleSearch()
    )

    url_tool = types.Tool(url_context=types.UrlContext())

    # Configure generation settings
    config = types.GenerateContentConfig(
        tools=[grounding_tool, url_tool]
    )

    # Make the request
    response = client.models.generate_content(
        model="gemini-2.5-flash",
        contents=f'''
        Analyze the top 5 Google search results for the keyword `{search_content}`.

**Objective:**
Identify the user search intent, content gaps, and existing content formats for the given keyword.

**Process:**
1.  Perform a Google search for `{search_content}` to retrieve the top 5 search results.
2.  For each of the top 5 URLs, analyze the content.
3.  Based on the analysis of all results, provide a summary that includes:
    *   **User Search Intent:** Determine the primary reason a user is searching for this keyword (e.g., informational, navigational, transactional, commercial investigation).
    *   **Existing Content Formats:** Identify the common formats of the top-ranking content (e.g., blog posts, videos, product pages, reviews, guides, news articles).
    *   **Content Gaps:** Pinpoint relevant subtopics, user questions, or angles that are not well-covered by the existing top results.

**Output:**
Return the findings in a structured JSON format as follows:

{{
  "keyword": "{search_content}",
  "analysis": {{
    "user_search_intent": "...",
    "existing_content_formats": [
      "...",
      "..."
    ],
    "content_gaps": [
      "...",
      "..."
    ]
  }},
  "top_5_results": [
    {{
      "rank": 1,
      "title": "...",
      "url": "...",
      "summary": "..."
    }},
    {{
      "rank": 2,
      "title": "...",
      "url": "...",
      "summary": "..."
    }},
    {{
      "rank": 3,
      "title": "...",
      "url": "...",
      "summary": "..."
    }},
    {{
      "rank": 4,
      "title": "...",
      "url": "...",
      "summary": "..."
    }},
    {{
      "rank": 5,
      "title": "...",
      "url": "...",
      "summary": "..."
    }}
  ]
}}

        ''',
        config=config,
    )

    # Print the grounded response
    cleared_text = response.text.strip('\n').strip('```').split('json')[1]
    try:
        dic = json.loads(cleared_text)
        print(dic)

        # Save data to CSV files
        save_to_csv(dic)

        return dic
    except Exception as e:
        print(f"Error parsing JSON: {e}")
        print(f"Raw response: {response.text}")
        return None


async def test_multiple_keywords():
    """Test the script with multiple keywords"""
    keywords = [
        "Anaplasma Antibody",
        "Brucella",
        "Campylobacter",
        "Clostridium",
        "Leptospira",
        "Paratyphoid"
    ]

    print(f"Testing {len(keywords)} keywords...")
    print("="*60)

    all_results = []

    for i, keyword in enumerate(keywords, 1):
        print(f"\n[{i}/{len(keywords)}] Processing: {keyword}")
        print("-" * 40)

        try:
            result = await get_browser_content(keyword)
            if result:
                # Save to CSV (append mode after first keyword)
                append_mode = i > 1
                save_to_csv(result, append_mode=append_mode)
                all_results.append(result)
                print(f"✓ Successfully processed: {keyword}")
            else:
                print(f"✗ Failed to process: {keyword}")
        except Exception as e:
            print(f"✗ Error processing {keyword}: {e}")

        # Add a small delay between requests to be respectful to the API
        if i < len(keywords):
            print("Waiting 5 seconds before next request...")
            await asyncio.sleep(5)

    print("\n" + "="*60)
    print(f"✓ Completed! Successfully processed {len(all_results)}/{len(keywords)} keywords")
    print("Check keyword_analysis.csv and keyword_results.csv for results")

    return all_results

# Run the test
asyncio.run(test_multiple_keywords())

