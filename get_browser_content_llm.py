from google import genai
from google.genai import types
import asyncio
import json
async def get_browser_content(search_content : str) -> str:
    # Configure the client
    client = genai.Client(api_key='AIzaSyBWPiNDni24gEzdZUzNUhVxws6prDHB4Yo')

    # Define the grounding tool
    grounding_tool = types.Tool(
        google_search=types.GoogleSearch()
    )

    url_tool = types.Tool(url_context=types.UrlContext())

    # Configure generation settings
    config = types.GenerateContentConfig(
        tools=[grounding_tool, url_tool]
    )

    # Make the request
    response = client.models.generate_content(
        model="gemini-2.5-flash",
        contents=f'''
        Analyze the top 5 Google search results for the keyword `{search_content}`.

**Objective:**
Identify the user search intent, content gaps, and existing content formats for the given keyword.

**Process:**
1.  Perform a Google search for `{search_content}` to retrieve the top 5 search results.
2.  For each of the top 5 URLs, analyze the content.
3.  Based on the analysis of all results, provide a summary that includes:
    *   **User Search Intent:** Determine the primary reason a user is searching for this keyword (e.g., informational, navigational, transactional, commercial investigation).
    *   **Existing Content Formats:** Identify the common formats of the top-ranking content (e.g., blog posts, videos, product pages, reviews, guides, news articles).
    *   **Content Gaps:** Pinpoint relevant subtopics, user questions, or angles that are not well-covered by the existing top results.

**Output:**
Return the findings in a structured JSON format as follows:

{{
  "keyword": "{search_content}",
  "analysis": {{
    "user_search_intent": "...",
    "existing_content_formats": [
      "...",
      "..."
    ],
    "content_gaps": [
      "...",
      "..."
    ]
  }},
  "top_5_results": [
    {{
      "rank": 1,
      "title": "...",
      "url": "...",
      "summary": "..."
    }},
    {{
      "rank": 2,
      "title": "...",
      "url": "...",
      "summary": "..."
    }},
    {{
      "rank": 3,
      "title": "...",
      "url": "...",
      "summary": "..."
    }},
    {{
      "rank": 4,
      "title": "...",
      "url": "...",
      "summary": "..."
    }},
    {{
      "rank": 5,
      "title": "...",
      "url": "...",
      "summary": "..."
    }}
  ]
}}

        ''',
        config=config,
    )

    # Print the grounded response
    cleared_text = response.text.strip('\n').strip('```').split('json')[1]
    try:
        dic = json.loads(cleared_text)
        print(dic)
    except:
        pass
    return 


asyncio.run(get_browser_content("COVID-19 Antigen Rapid Test"))

